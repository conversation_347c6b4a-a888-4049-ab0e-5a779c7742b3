<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>异地组网配置弹窗 - 移动端优化演示</title>
    <style>
      * {
        box-sizing: border-box;
      }
      body {
        padding: 20px;
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei";
        background: #f5f5f5;
      }
      .demo-container {
        max-width: 400px;
        padding: 20px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
      }
      .demo-title {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        text-align: center;
      }
      .form-item {
        margin-bottom: 28px;
      }
      .form-label {
        display: block;
        width: 100%;
        padding-bottom: 12px;
        font-size: 15px;
        font-weight: 600;
        line-height: 1.4;
        color: #303133;
        word-break: break-word;
      }
      .form-content {
        width: 100%;
      }

      /* 开关组件样式 */
      .switch-container {
        display: flex;
        align-items: center;
        width: 100%;
        margin: 12px 0;
      }
      .switch {
        position: relative;
        order: 2;
        width: 50px;
        height: 24px;
        margin: 0 12px;
        cursor: pointer;
        background: #dcdfe6;
        border-radius: 12px;
        transition: background 0.3s;
      }
      .switch.active {
        background: #409eff;
      }
      .switch-handle {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s;
      }
      .switch.active .switch-handle {
        transform: translateX(26px);
      }
      .switch-label-left {
        order: 1;
        font-size: 14px;
        color: #606266;
      }
      .switch-label-right {
        order: 3;
        font-size: 14px;
        color: #606266;
      }

      /* IP输入框组合样式 */
      .ip-input-row {
        display: flex;
        gap: 10px;
        align-items: flex-start;
        margin-bottom: 15px;
      }
      .ip-input {
        flex: 1;
        min-width: 0;
      }
      .input {
        width: 100%;
        height: 42px;
        padding: 0 12px;
        font-size: 14px;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        transition: border-color 0.2s;
      }
      .input:focus {
        border-color: #409eff;
        outline: none;
      }
      .input::placeholder {
        font-size: 13px;
        color: #c0c4cc;
      }
      .btn-circle {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 42px;
        font-size: 16px;
        color: white;
        cursor: pointer;
        background: #f56c6c;
        border: 1px solid #f56c6c;
        border-radius: 50%;
        transition: all 0.2s;
      }
      .btn-circle:hover {
        background: #f78989;
      }
      .btn-circle.primary {
        background: #409eff;
        border-color: #409eff;
      }
      .btn-circle.primary:hover {
        background: #66b1ff;
      }
      .add-btn {
        margin-top: 10px;
      }

      /* 按钮组样式 */
      .button-group {
        display: flex;
        gap: 15px;
        justify-content: center;
        padding-top: 25px;
        margin-top: 30px;
        border-top: 1px solid #ebeef5;
      }
      .btn {
        flex: 1;
        max-width: 140px;
        height: 44px;
        padding: 12px 24px;
        font-size: 15px;
        font-weight: 600;
        color: #606266;
        cursor: pointer;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        transition: all 0.2s;
      }
      .btn.primary {
        color: white;
        background: #409eff;
        border-color: #409eff;
      }
      .btn:hover {
        opacity: 0.8;
      }

      /* 小屏幕优化 */
      @media screen and (width <= 480px) {
        .demo-container {
          padding: 16px;
          margin: 0;
          border-radius: 8px;
        }
        .form-item {
          margin-bottom: 24px;
        }
        .form-label {
          padding-bottom: 10px;
          font-size: 14px;
        }
        .switch {
          width: 45px;
          height: 22px;
        }
        .switch-handle {
          width: 18px;
          height: 18px;
        }
        .switch.active .switch-handle {
          transform: translateX(23px);
        }
        .switch-label-left,
        .switch-label-right {
          font-size: 13px;
        }
        .ip-input-row {
          gap: 8px;
          margin-bottom: 12px;
        }
        .input {
          height: 38px;
          padding: 0 10px;
          font-size: 13px;
        }
        .btn-circle {
          width: 38px;
          height: 38px;
          font-size: 14px;
        }
        .button-group {
          flex-direction: column;
          gap: 10px;
          padding-top: 20px;
          margin-top: 24px;
        }
        .btn {
          width: 100%;
          max-width: none;
          height: 42px;
          font-size: 14px;
        }
      }
      .status-badge {
        display: inline-block;
        padding: 4px 8px;
        margin-bottom: 20px;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
      }
      .status-optimized {
        color: #409eff;
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
      }
      .feature-list {
        padding: 0;
        margin: 20px 0;
        list-style: none;
      }
      .feature-list li {
        padding: 8px 0;
        font-size: 14px;
        color: #606266;
      }
      .feature-list li::before {
        margin-right: 8px;
        font-weight: bold;
        color: #67c23a;
        content: "✓";
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <div class="demo-title">异地组网设备配置</div>

      <div class="status-badge status-optimized">✨ 移动端布局已优化</div>

      <div class="form-item">
        <label class="form-label">网段生成模式</label>
        <div class="form-content">
          <div class="switch-container">
            <span class="switch-label-left">自动生成</span>
            <div class="switch active" onclick="toggleSwitch(this)">
              <div class="switch-handle"></div>
            </div>
            <span class="switch-label-right">自定义</span>
          </div>
        </div>
      </div>

      <div class="form-item">
        <label class="form-label">允许访问IP段</label>
        <div class="form-content">
          <div class="ip-input-row">
            <div class="ip-input">
              <input type="text" class="input" placeholder="请输入IP地址段，如 ***********/24" value="***********/24" />
            </div>
            <button class="btn-circle" onclick="removeIp(this)" title="删除">−</button>
          </div>

          <div class="ip-input-row">
            <div class="ip-input">
              <input type="text" class="input" placeholder="请输入IP地址段，如 10.0.0.0/8" value="10.0.0.0/8" />
            </div>
            <button class="btn-circle" onclick="removeIp(this)" title="删除">−</button>
          </div>

          <button class="btn-circle primary add-btn" onclick="addIp()" title="添加">+</button>
        </div>
      </div>

      <div class="button-group">
        <button class="btn primary" onclick="confirm()">确定</button>
        <button class="btn" onclick="cancel()">取消</button>
      </div>

      <div style="padding: 15px; margin-top: 30px; background: #f8f9fa; border-radius: 8px">
        <div style="margin-bottom: 10px; font-weight: 600; color: #303133">✨ 优化特性</div>
        <ul class="feature-list">
          <li>响应式标签布局，避免文字截断</li>
          <li>开关组件对齐优化，支持长文本</li>
          <li>IP输入框弹性布局，操作更便捷</li>
          <li>按钮组自适应，小屏垂直排列</li>
          <li>增强的视觉反馈和用户体验</li>
        </ul>
      </div>
    </div>

    <script>
      function toggleSwitch(element) {
        element.classList.toggle("active");
      }

      function removeIp(button) {
        const row = button.closest(".ip-input-row");
        const container = row.parentNode;
        if (container.querySelectorAll(".ip-input-row").length > 1) {
          row.remove();
        } else {
          alert("至少需要保留一个IP地址段");
        }
      }

      function addIp() {
        const container = document.querySelector(".form-content");
        const addBtn = container.querySelector(".add-btn");

        const newRow = document.createElement("div");
        newRow.className = "ip-input-row";
        newRow.innerHTML = `
                <div class="ip-input">
                    <input type="text" class="input" placeholder="请输入IP地址段，如 ***********/24">
                </div>
                <button class="btn-circle" onclick="removeIp(this)" title="删除">
                    −
                </button>
            `;

        container.insertBefore(newRow, addBtn);
      }

      function confirm() {
        alert("配置保存成功！\n\n在实际应用中，这里会提交表单数据并关闭弹窗。");
      }

      function cancel() {
        if (confirm("确定要取消配置吗？")) {
          alert("已取消配置");
        }
      }
    </script>
  </body>
</html>
