<template>
  <div class="guide-button">
    <el-tooltip :content="t('guide.title')" placement="bottom">
      <i class="iconfont icon-yiwen toolBar-icon" @click="handleGuideClick"></i>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts" name="Guide">
import { useI18n } from "vue-i18n";
import { useGuide } from "@/hooks/useGuide";

const { t } = useI18n();
const { startAutoGuide } = useGuide();

const handleGuideClick = async () => {
  console.log("Guide button clicked");

  try {
    await startAutoGuide({
      allowClose: true,
      animate: true,
      opacity: 0.75,
      padding: 10
    });
  } catch (error) {
    console.error("Error starting guide from header:", error);
  }
};
</script>

<style scoped lang="scss">
.guide-button {
  cursor: pointer;
  transition: all 0.3s ease;

  .toolBar-icon {
    transition: color 0.3s ease;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
