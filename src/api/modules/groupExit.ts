import http from "@/api";
import { PORT1 } from "@/api/config/servicePort";

/**
 * 保存出口设备信息
 * @param params 参数对象，包含groupId和exitDeviceId
 * @returns 保存结果
 */
export const saveGroupExit = (params: { groupId: string; exitDeviceId: string }[]) => {
  return http.post(PORT1 + `/groupExit/saveGroupExit`, params);
};

/**
 * 根据分组ID删除出口设备信息
 * @param params 参数对象，包含groupId
 * @returns 删除结果
 */
export const deleteExitByGroupId = (params: { groupId: string }) => {
  return http.post(PORT1 + `/groupExit/deleteExitByGroupId`, params);
};

/**
 * 根据设备ID删除出口设备信息
 * @param params 参数对象，包含exitDeviceId
 * @returns 删除结果
 */
export const deleteExitByDeviceId = (params: { exitDeviceId: string }) => {
  return http.post(PORT1 + `/groupExit/deleteExitByDeviceId`, params);
};

/**
 * 根据分组ID查询出口设备信息
 * @param groupId 分组ID
 * @returns 出口设备信息
 */
export const getExitByGroupId = (groupId: string) => {
  return http.get(PORT1 + `/groupExit/getExitByGroupId`, { groupId });
};
