import { Login } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
// import authMenuList from "@/assets/json/authMenuList.json";
// import authButtonList from "@/assets/json/authButtonList.json";
import http from "@/api";
import { getDeviceInfo } from "../../utils/device";

/**
 * @name 登录模块
 */
// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
  const { deviceName, deviceType, uuid } = getDeviceInfo();
  return http.post<Login.ResLogin>(PORT1 + `/login`, params, {
    loading: false,
    headers: {
      deviceName,
      deviceType,
      uuid
    }
  });
  // return http.post<Login.ResLogin>(PORT1 + `/login`, params, { loading: false }); // 正常 post json 请求  ==>  application/json
  // return http.post<Login.ResLogin>(PORT1 + `/login`, params, { loading: false }); // 控制当前请求不显示 loading
  // return http.post<Login.ResLogin>(PORT1 + `/login`, {}, { params }); // post 请求携带 query 参数  ==>  ?username=admin&password=123456
  // return http.post<Login.ResLogin>(PORT1 + `/login`, qs.stringify(params)); // post 请求携带表单参数  ==>  application/x-www-form-urlencoded
  // return http.get<Login.ResLogin>(PORT1 + `/login?${qs.stringify(params, { arrayFormat: "repeat" })}`); // get 请求可以携带数组等复杂参数
};

// 获取菜单列表
export const getAuthMenuListApi = () => {
  return http.get<Menu.MenuOptions[]>(PORT1 + `/webMenu/queryWebMenu`, {}, { loading: false });
  // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
  // return authMenuList;
};

// 获取按钮权限
export const getAuthButtonListApi = () => {
  // return http.get<Login.ResAuthButtons>(PORT1 + `/auth/buttons`, {}, { loading: false });
  // 如果想让按钮权限变为本地数据，注释上一行代码，并引入本地 authButtonList.json 数据
  return [];
};

// 用户退出登录
export const logoutApi = () => {
  return http.post(PORT1 + `/signOut`);
};

/** 获取当前用户信息 */
export const getUserInfo = () => {
  return http.get<any>("/users/getUserInfo");
};

/**
 * 发送邮箱验证码
 * @param emailOrPhone 邮箱或手机号
 */
export const sendCodeApi = (emailOrPhone: string) => {
  return http.post(PORT1 + "/users/send", { email: emailOrPhone });
};

/**
 * 发送短信验证码
 * @param emailOrPhone 邮箱或手机号
 */
export const sendSmsCodeApi = (emailOrPhone: string) => {
  return http.post(PORT1 + "/users/getSmsCode", { phone: emailOrPhone });
};

// 邮箱正则表达式
const emailRegex = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
// 手机号正则表达式（中国大陆手机号）
const phoneRegex = /^1[3-9]\d{9}$/;

/**
 * 判断用户是否已经注册
 * @param emailOrPhone 邮箱或手机号
 */
export const isRegisterApi = (emailOrPhone: string) => {
  if (emailRegex.test(emailOrPhone)) return http.post("/users/userExists", { email: emailOrPhone });
  if (phoneRegex.test(emailOrPhone)) return http.post("/users/userExists", { phone: emailOrPhone });
};

/**
 * 注册和验证码登录接口
 * @param data 注册或验证码登录参数
 */
export const registerApi = (data: Login.ReqRegisterForm | Login.ReqSmsLoginForm) => {
  const { deviceName, deviceType, uuid } = getDeviceInfo();
  return http.post<Login.ResLogin>(PORT1 + "/sms-login", data, {
    loading: false,
    headers: {
      deviceName,
      deviceType,
      uuid
    }
  });
};

/**
 * 保存新用户密码
 */
export const saveNewPasswordApi = (data: Login.ReqSaveNewPassword) => {
  return http.post("/users/saveUserPassword", data);
};

/**
 * 重置密码
 */
export const resetPasswordApi = (data: Login.ReqRegisterForm) => {
  return http.post("/users/forgetPass", data);
};
