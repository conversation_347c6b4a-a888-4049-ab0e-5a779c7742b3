import { useUserStore } from "@/stores/modules/user";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { DeleteBridgeClientParams, GetTopologyParams, Params, SaveDeviceNameParams } from "@/api/interface/topology";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * 获取设备拓扑数据
 * @param deviceId 设备ID
 * @returns 设备拓扑数据
 */
export const getTopologyData = async (deviceId: string) => {
  const params: GetTopologyParams = {
    cmd: 4,
    deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["topology"]
    }
  };

  console.log("getTopologyData params:", JSON.stringify(params));

  return await getDeviceTopology(params);
};

/**
 * 保存设备名称
 * @param deviceId 设备ID
 * @param name 设备名称
 * @param macaddr MAC地址
 * @returns 保存结果
 */
export const saveDeviceName = async (deviceId: string, name: string, macaddr: string) => {
  const params: SaveDeviceNameParams = {
    cmd: 6,
    deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            name,
            macaddr
          }
        ]
      }
    }
  };

  return await pushDeviceConfigJwe(params);
};

/**
 * 删除网桥客户端
 * @param deviceId 设备ID
 * @param macaddr MAC地址
 * @returns 删除结果
 */
export const deleteBridgeClient = async (deviceId: string, macaddr: string) => {
  const params: DeleteBridgeClientParams = {
    cmd: 6,
    deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            macaddr,
            delete: 1
          }
        ]
      }
    }
  };

  return await pushDeviceConfigJwe(params);
};

/**
 * 获取项目设备列表
 * @param params 请求参数
 * @returns 设备列表
 */
export const getDeviceListByGroupId = (params: Params) => {
  // 确保 groupId 是字符串而不是数组
  const groupId = params.groupId;

  // 确保使用字符串类型的 groupId
  const cleanParams = { groupId: Array.isArray(groupId) ? groupId[0] : groupId };

  // 添加 cancel: true 参数，允许请求被取消，避免重复请求
  return http.get<any[]>(PORT1 + `/device/queryDevicesByGroupId`, cleanParams, { cancel: true });
};

export const getDeviceTopology = (params: any) => {
  return http.post<any[]>(PORT1 + `/deviceTopology/getDeviceTopology`, params, { cancel: false, loading: false });
};

/**
 * 保存分组出口设备
 * @param params 出口设备参数数组
 * @returns 保存结果
 */
export const saveGroupExit = (params: any[]) => {
  return http.post<any>(PORT1 + `/groupExit/saveGroupExit`, params, { cancel: false });
};

/**
 * 根据分组ID删除出口设备
 * @param params 分组ID参数
 * @returns 删除结果
 */
export const deleteExitByGroupId = (params: { groupId: string | number }) => {
  return http.post<any>(PORT1 + `/groupExit/deleteExitByGroupId`, params, { cancel: false });
};

/**
 * 根据设备ID删除出口设备
 * @param params 设备ID参数
 * @returns 删除结果
 */
export const deleteExitByDeviceId = (params: { exitDeviceId: string }) => {
  return http.post<any>(PORT1 + `/groupExit/deleteExitByDeviceId`, params, { cancel: false });
};

/**
 * 根据分组ID获取出口设备
 * @param groupId 分组ID
 * @returns 出口设备列表
 */
export const getExitByGroupId = (groupId: string | number) => {
  return http.get<any[]>(PORT1 + `/groupExit/getExitByGroupId`, { groupId }, { cancel: false });
};
