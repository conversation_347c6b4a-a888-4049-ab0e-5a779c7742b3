import http from "@/api";
import { PORT1 } from "@/api/config/servicePort";
// import { DeviceWeekStatistics } from "@/api/interface/device/model";

export const deviceTrafficWeeklyReport = async (params: { deviceId: string }) => {
  return http.get<any>(PORT1 + `/deviceStatistics/deviceTrafficWeeklyReport`, params);
};

export const deviceTrafficHourlyReport = (params: { deviceId: string }) => {
  return http.get(PORT1 + `/deviceStatistics/deviceTrafficHourlyReport`, params);
};
