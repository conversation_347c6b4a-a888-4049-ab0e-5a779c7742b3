import http from "@/api";
import { PORT1 } from "@/api/config/servicePort";

export const getMessage = (params: { id: string }) => {
  return http.get(PORT1 + `/message/list`, params);
};

export const getUnreadMessages = () => {
  return http.get(PORT1 + `/message/queryUnreadMessages`, {}, { cancel: false });
};

export const updateReadFlag = (params: string[]) => {
  return http.post(PORT1 + `/message/batchUpdateReadFlag`, params);
};

// 标记全部已读
export const markAllAsRead = () => {
  return http.post(PORT1 + `/message/updateAllReadFlag`);
};
