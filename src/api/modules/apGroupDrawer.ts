import type { Project } from "@/api/interface/project";
import i18n from "@/languages";
import { useUserStore } from "@/stores/modules/user";
import { getDeviceConfigJwe, pushDeviceConfigJwe } from "@/api/modules/project";

const t = i18n.global.t;

// 获取AP组列表数据
export const getApGroupList = async (deviceId: string) => {
  const params: Project.ReqConfigParams = {
    cmd: 10,
    deviceId: deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      system: ["apGroup"]
    }
  };
  console.log("getApGroupList params:", JSON.stringify(params));
  return await getDeviceConfigJwe(params);
};

// 打开AP组管理抽屉

export const createApGroupDrawerParams = (
  title: string,
  row: Partial<Project.ReqProjectParams> = {},
  getTableList?: () => void
): {
  isView: boolean;
  title: string;
  row: {
    groupId?: number;
    groupName?: string;
    deviceId?: string;
    orderNum?: number;
    unifiedManageFlag?: number;
    configurationItem?: any;
    current?: number;
    size?: number;
  };
  api: any;
  getTableList: () => void;
} => {
  // console.log("createApGroupDrawerParams row:", JSON.stringify(row));
  // const result = await getApGroupList(row.deviceId);
  // console.log("getApGroupList result:", JSON.stringify(result));
  // if (result.code !== "200") {
  //   return {
  //     title,
  //     isView: title.includes(t("common.view")) || title.includes("view"),
  //     row: { ...row },
  //     api: saveApConfig,
  //     getTableList
  //   };
  // }
  return {
    isView: title.includes(t("common.view")) || title.includes("view"),
    title,
    row: { ...row },
    api: saveApGroup,
    getTableList
  };
};

// 保存AP分组配置
export const saveApGroup = async (params: { scope: { deviceId: string; row: any }; row: any }) => {
  try {
    console.log("保存AP配置参数:", JSON.stringify(params));

    // 确保 row 对象存在
    if (!params.row) {
      params.row = {};
    }

    const reqParams: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: params.scope.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: {
          apGroup: params.row.apGroup
        }
      }
    };
    console.log("请求参数:", JSON.stringify(reqParams));
    const response = await pushDeviceConfigJwe(reqParams);
    if (response.code === "200") {
      console.log("保存成功");
    } else {
      console.error("保存失败:", response.msg);
    }
    return response;
  } catch (err) {
    console.error("保存AP配置时发生错误:", err);
    throw err;
  }
};
