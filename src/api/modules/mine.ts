import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

export const updateNickName = (params: { nickName: string }) => {
  return http.post(PORT1 + `/users/updateNickName`, params);
};

export const updateEmail = (params: { email: string; code: string }) => {
  return http.post(PORT1 + `/users/updateEmail`, params);
};

export const updatePhone = (params: { phone: string; code: string }) => {
  return http.post(PORT1 + `/users/updatePhone`, params);
};

// 修改密码接口,需要输入旧密码和新密码
export const updatePassword = (params: { phone: string; code: string; userId: string; password: string }) => {
  return http.post(PORT1 + `/users/updateUser`, params);
};

// 反馈接口
export const saveFeedback = (params: { feedback: string; contact: string }) => {
  return http.post(PORT1 + "/feedback/addFeedback", params);
};

// 重置密码接口,需要输入新密码

export interface PhoneParams {
  phone: string;
  code?: string;
}

export interface EmailParams {
  email: string;
  code?: string;
}

export interface PasswordParams {
  password: string;
}

export const validationVerificationCode = (params: PhoneParams | EmailParams | PasswordParams) => {
  return http.post(PORT1 + `/users/validationVerificationCode`, params);
};

export const sendEmailCode = (params: { email: string }) => {
  return http.post(PORT1 + `/users/send`, params);
};

export const sendPhoneCode = (params: { phone: string }) => {
  return http.post(PORT1 + `/users/getSmsCode`, params);
};

export const getNewestAgreement = (params: { type: number }, headers: Record<string, string> = {}) => {
  return http.get(PORT1 + `agreement/getNewestAgreement/v2`, params, { headers });
};
