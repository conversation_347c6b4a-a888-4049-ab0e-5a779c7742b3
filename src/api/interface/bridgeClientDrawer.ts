import { computed, nextTick, reactive, ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { pushDeviceConfigJwe } from "@/api/modules/project";
import { ElMessage } from "element-plus";
import type { Project } from "@/api/interface/project";
import bridgeIcon_main from "@/assets/images/bridge_center_icon.png";
import bridgeIcon_onLine from "@/assets/images/device3_icon.png";
import bridgeIcon_offline from "@/assets/images/device4_icon.png";
import otherDeviceIcon_online from "@/assets/images/device_other_online_icon.png";
import otherDeviceIcon_offline from "@/assets/images/device_other_offline_icon.png";
import internetIcon from "@/assets/images/internet_icon.png";
import { useGlobalStore } from "@/stores/modules/global";

export interface BridgeClientData extends Partial<Project.ResDeviceList> {
  extra?: {
    type?: number;
    model?: string;
    version?: string;
    macaddr?: string;
    txByte?: number;
    rxByte?: number;
    txRate?: number;
    rxRate?: number;
    latency?: number;
    time?: number;
    rssi?: number;
    online?: number;
  };
  name?: string;
  brClient?: any[];
}

export interface DrawerProps {
  title: string;
  isView: boolean;
  row: BridgeClientData;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

export const drawerVisible = ref(false);
export const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

export const clickNodeProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

export const data = reactive({
  name: "internet",
  symbol: "image://" + internetIcon,
  children: []
});

export const editName = ref(false); // 是否编辑网桥客户端名称
export const deviceNameChanged = ref(false); // 是否修改网桥客户端名称

export let bridgeClientDrawerVisible = ref(false);
export const editDeviceName = (row: any) => {
  console.log("editDeviceName called. row:", row);
  editName.value = !editName.value;
};

// 格式化 bootTime 方法
export const formatBootTime = (bootTimeInSeconds: number): string => {
  const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
  const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
  const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
  const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
  const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
  const seconds = bootTimeInSeconds % 60;

  // 获取浏览器语言
  const globalStore = useGlobalStore();
  const isChinese = computed(() => globalStore.language === "zh"); // 使用全局语言

  // 以数组的形式存储各部分
  const timeParts: string[] = [];

  if (years > 0) {
    timeParts.push(`${years}${isChinese.value ? "年" : "yr"}`);
  }
  if (months > 0) {
    timeParts.push(`${months}${isChinese.value ? "月" : "mo"} `);
  }
  if (days > 0) {
    timeParts.push(`${days}${isChinese.value ? "天" : "d"} `);
  }
  if (hours > 0) {
    timeParts.push(`${hours}${isChinese.value ? "时" : "h"} `);
  }
  if (minutes > 0) {
    timeParts.push(`${minutes}${isChinese.value ? "分" : "min"} `);
  }
  if (seconds > 0 || timeParts.length === 0) {
    timeParts.push(`${seconds}${isChinese.value ? "秒" : "s"} `);
  }

  // 拼接时间部分，使用空格分隔
  return timeParts.join("");
};

const loadImageAsBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url);
  const blob = await response.blob();
  return new Promise(resolve => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.readAsDataURL(blob);
  });
};

export const generateData = async (row: any, t: any) => {
  // 动态加载 bridgeIcon_main 为 Base64
  const bridgeIconMainBase64 = await loadImageAsBase64(bridgeIcon_main);

  // 更新数据
  data.children = [
    {
      name: row.deviceName ? row.deviceName : row.deviceId,
      symbol: `image://${bridgeIconMainBase64}`, // 使用 Base64
      children: [],
      extra: {}
    }
  ];

  if (row.brClient) {
    let children = [];
    row.brClient.sort((a: any, b: any) => b.online - a.online);
    row.brClient.forEach((item: any) => {
      children.push({
        name: item.name ? item.name : t("terminal.terminalType.unknown"),
        symbol: "image://" + getDeviceIcon(item.type, item.online),
        extra: item
      });
    });
    data.children[0].children = children;
  }

  // 确保视图更新
  await nextTick();
  console.log("DOM 已更新，当前 data.children:", data.children);
};

const getDeviceIcon = (type: number, online: number) => {
  switch (type) {
    case 1:
      return online === 1 ? bridgeIcon_onLine : bridgeIcon_offline;
    default:
      return online === 1 ? otherDeviceIcon_online : otherDeviceIcon_offline;
  }
};

export const saveDeviceName = async (row: any, t: any) => {
  console.log("saveDeviceName called. row:", JSON.stringify(row));
  const params = {
    cmd: 6,
    deviceId: drawerProps.value.row.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            name: row.name,
            macaddr: row.extra.macaddr
          }
        ]
      }
    }
  };
  console.log("发送的参数:", params);
  const response = await pushDeviceConfigJwe(params);
  console.log("API 响应数据:", response);
  if (!response || response.code !== "200") {
    ElMessage.error({ message: response.msg });
    return;
  }
  ElMessage.success({ message: t("common.operationSuccess") });
};

export const deleteBridgeClient = async (row: any, t: any) => {
  console.log("deleteBridgeClient called. row:", JSON.stringify(row));
  const params = {
    cmd: 6,
    deviceId: drawerProps.value.row.deviceId,
    userId: useUserStore().userInfo.userId,
    data: {
      network: {
        brClient: [
          {
            macaddr: row.extra.macaddr,
            delete: 1
          }
        ]
      }
    }
  };
  console.log("发送的参数:", params);
  const response = await pushDeviceConfigJwe(params);
  console.log("API 响应数据:", response);
  if (!response || response.code !== "200") {
    ElMessage.error({ message: response.msg });
    return;
  }
  ElMessage.success({ message: t("common.operationSuccess") });
};
