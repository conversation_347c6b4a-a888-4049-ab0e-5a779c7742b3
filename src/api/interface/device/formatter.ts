import i18n from "@/languages";
import { useI18n } from "vue-i18n";
import { SpeedDuplex } from "@/api/interface/device/model";
const t = i18n.global.t;

export const getModeText = (mode?: number): string => {
  switch (mode) {
    case 0:
      return t("device.unlocking");
    case 1:
      return t("device.timeLocking");
    case 2:
      return t("device.alwaysLock");
    default:
      return t("device.unknown");
  }
};

export function getDescription(key: string): string {
  const t = i18n.global.t;
  switch (key) {
    case "UNICAST":
      return t("common.unicast"); // UNICAST 对应的中文描述
    case "MULTICAST":
      return t("common.multicast"); // MULTICAST 对应的中文描述
    case "BROADCAST":
      return t("common.broadcast"); // BROADCAST 对应的中文描述
    default:
      return "";
  }
}

// 端口连接的情况
export const formatSpeedDuplex = (speedDuplex: number) => {
  const { t } = useI18n();
  switch (speedDuplex) {
    case SpeedDuplex.TenHalf:
      return t("common.TenHalf");
    case SpeedDuplex.TenFull:
      return t("common.TenFull");
    case SpeedDuplex.HundredHalf:
      return t("common.HundredHalf");
    case SpeedDuplex.HundredFull:
      return t("common.HundredFull");
    case SpeedDuplex.ThousandFull:
      return t("common.ThousandFull");
    case SpeedDuplex.TwoThousandFiveFull:
      return t("common.TwoThousandFiveFull");
    case SpeedDuplex.FiveThousandFull:
      return t("common.FiveThousandFull");
    case SpeedDuplex.TenThousandFull:
      return t("common.TenThousandFull");
  }
};

export const formatBytes = (bytes: number | null | undefined): string => {
  if (bytes === null || bytes === undefined) {
    return "--"; // 数据为空时返回占位符
  }

  const units = ["B", "KB", "MB", "GB", "TB", "PB"];
  let unitIndex = 0;
  let value = bytes;

  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }

  return `${value.toFixed(2)} ${units[unitIndex]}`;
};
