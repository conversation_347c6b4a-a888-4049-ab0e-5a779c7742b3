// 请求响应参数（不包含data）
export interface Result {
  code: string;
  msg: string;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  access_token: string;
  bootTime: Number;
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  list: T[];
  current: number;
  size: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  current: number;
  size: number;
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    fileUrl: string;
  }
}

// 工具模块
export namespace Tools {
  // MAC 地址查询响应
  export interface MacInfoResponse {
    organization: string;
    organizationAddress: string;
    // 可以根据实际 API 响应添加其他字段
  }
}

// 登录模块
export namespace Login {
  /** 登录请求参数 */
  export interface ReqLoginForm {
    username: string;
    password: string;
  }

  /** 登录响应数据 */
  export interface ResLogin {
    access_token: string;
    msg: string;
  }

  /** 验证码登录请求参数 */
  export interface ReqSmsLoginForm {
    phone: string; // 手机号或邮箱，后端会自动区分
    code: string;
  }

  /** 注册请求参数 */
  export interface ReqRegisterForm {
    emailOrPhone: string;
    password: string;
    confirmPassword: string;
    code: string;
    nickname: string;
  }

  export interface ResAuthButtons {
    [key: string]: string[];
  }

  export interface ReqSaveNewPassword {
    password: string;
    confirmPassword: string;
  }
}

// 用户管理模块
export namespace User {
  export interface ReqUserParams extends ReqPage {
    username: string;
    gender: number;
    idCard: string;
    email: string;
    address: string;
    createTime: string[];
    status: number;
  }
  export interface ResUserList {
    id: string;
    username: string;
    gender: number;
    user: { detail: { age: number } };
    idCard: string;
    email: string;
    address: string;
    createTime: string;
    status: number;
    avatar: string;
    photo: any[];
    children?: ResUserList[];
  }
  export interface ResStatus {
    userLabel: string;
    userValue: number;
  }
  export interface ResGender {
    genderLabel: string;
    genderValue: number;
  }
  export interface ResDepartment {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
  export interface ResRole {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
}
