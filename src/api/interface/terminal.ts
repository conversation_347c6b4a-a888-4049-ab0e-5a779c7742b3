// 定义接口
import type { Project } from "@/api/interface/project";

export interface ExpandedRowData {
  userList: any[];
  apList: any[];
}

export interface TerminalDrawerParams {
  title: string;
  isView: boolean;
  row: Partial<Project.ReqProjectParams>;
  scope: any;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

export interface ApDrawerParams {
  title: string;
  isView: boolean;
  row: Partial<Project.ReqProjectParams>;
  parentRow: Partial<Project.ReqProjectParams>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

export interface BridgeClientDrawerParams {
  title: string;
  isView: boolean;
  row: Partial<Project.ReqProjectParams>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

export interface ApGroupDrawerParams {
  title: string;
  isView: boolean;
  row: Partial<Project.ReqProjectParams>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}
