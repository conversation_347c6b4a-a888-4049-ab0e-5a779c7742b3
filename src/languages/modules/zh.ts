export default {
  user: {
    userName: "用户名",
    userNameTip: "请输入用户名",
    password: "密码",
    passwordTip: "请输入密码",
    emailOrPhone: "邮箱/手机号",
    emailOrPhoneTip: "请输入邮箱/手机号",
    emailOrPhoneErrorTip: "邮箱/手机号格式不正确",
    phoneFormatError: "手机号格式不正确",
    phoneErrorTip: "手机号格式不正确",
    code: "验证码",
    codeTip: "请输入验证码",
    codeRuleTip: "验证码必须是4位数字",
    confirmPassword: "确认密码",
    confirmPasswordTip: "请再次输入密码",
    passwordRuleTip: "密码必须包含字母和数字，且长度不少于8位",
    passwordNotMatch: "两次输入的密码不一致",
    nickname: "昵称",
    inputNickname: "请输入昵称",
    nicknameTip: "请输入昵称",
    nicknameRuleTip: "昵称长度不能超过50个字符",
    personalInfo: "个人信息",
    basicInfo: "基本信息",
    accountLogin: "账号密码登录",
    codeLogin: "验证码登录",
    noAccount: "还没有账号？",
    registerNow: "立即注册",
    register: "注册",
    sendCode: "发送验证码",
    sendCodeFail: "发送验证码失败",
    codeSent: "验证码已发送",
    codeValidTime: "验证码有效期3分钟",
    resendCodeTime: "重新发送({time}s)",
    registerSuccess: "注册成功",
    registerFail: "注册失败",
    setNewPassword: "设置新密码",
    setNewPasswordTip: "请输入新密码",
    phone: "手机号",
    phoneTip: "请输入手机号",
    notCurrentUserPhone: "不是当前用户绑定手机号",
    email: "邮箱",
    newEmail: "新邮箱",
    emailTip: "请输入邮箱",
    notCurrentUserEmail: "不是当前用户绑定邮箱",
    deleteAccount: "注销账号",
    deleteAccountTip: "是否注销账号？",
    deleteAccountSuccess: "注销账号成功",
    verifyIdentity: "验证身份",
    setNewEmail: "设置新邮箱",
    setPassword: "设置密码",
    setNewEmailTip: "请输入新邮箱",
    setNewEmailSuccess: "设置新邮箱成功",
    setNewPhone: "设置新手机号",
    setNewPhoneTip: "请输入新手机号",
    setNewPhoneSuccess: "设置新手机号成功",
    done: "完成",
    resendCode: "重新发送",
    sendCodeSuccess: "发送验证码成功",
    confirmRegist: "是否跳转到注册页面？",
    userExist: "用户已存在",
    userNotExist: "用户不存在",
    forgetPassword: "忘记密码",
    resetPassword: "重置密码",
    emailBeenBind: "邮箱已被绑定",
    phoneBeenBind: "手机号已被绑定",
    emailNotEmpty: "邮箱不能为空",
    codeNotEmpty: "验证码不能为空",
    emailSame: "新邮箱与原邮箱相同",
    codeFormatTip: "验证码格式错误",
    ssidMaxLength: "SSID长度不能超过32位"
  },
  common: {
    refresh: "刷新",
    deviceStatistics: "设备统计",
    reset: "重置",
    confirm: "确定",
    cancel: "取消",
    save: "保存",
    delete: "删除",
    edit: "编辑",
    add: "添加",
    search: "搜索",
    modify: "修改",
    binding: "绑定",
    modifyBinding: "修改绑定",
    feedback: "意见反馈",
    feedbackTip: "请输入您的反馈内容",
    contact: "联系方式",
    contactTip: "请留下您的联系方式（选填）",
    userAgreement: "用户协议",
    privacyPolicy: "隐私政策",
    quickLinks: "快捷链接",
    resetPassword: "重置密码",
    resetPasswordSuccess: "重置密码成功",
    resetPasswordFail: "重置密码失败",
    resetPasswordConfirm: "是否重置密码？",
    blackList: "黑名单",
    addBlackList: "加入黑名单",
    view: "查看",
    deleteConfirm: "是否删除？",
    deleteSuccess: "删除成功",
    deleteFail: "删除失败",
    deleteFailTip: "删除失败",
    yes: "是",
    no: "否",
    networkLimit: "网络限速",
    internetControl: "上网控制",
    internet: "互联网",
    auto: "自动",
    invalidHostFormat: "无效的主机格式，请输入有效的IP地址或域名",
    pingHost: "请输入主机地址",
    pingCount: "请输入Ping次数",
    pingInterval: "请输入Ping间隔",
    pingCountError: "Ping次数必须大于0",
    pingIntervalError: "Ping间隔必须大于100ms",
    startPing: "开始Ping",
    sequenceNumber: "序列号",
    pingTime: "时间",
    pingResult: "结果",
    pingDelay: "延迟",
    pingTTL: "TTL",
    packetsSent: "发送包数",
    packetsReceived: "接收包数",
    packetLoss: "丢包率",
    averageDelay: "平均延迟",
    success: "成功",
    failed: "失败",
    pingTestFailed: "Ping测试失败",
    invalidDataFormat: "无效的数据格式",
    invalidDelayFormat: "无效的延迟格式",
    portScan: "端口扫描",
    portScanDescription: "扫描指定主机的端口，发现开放的端口和服务",
    ipAddress: "IP地址",
    portRange: "端口范围",
    startingPort: "起始端口",
    endingPort: "结束端口",
    startingScan: "开始扫描",
    scanResults: "扫描结果",
    portsFound: "个端口开放",
    scanning: "扫描中",
    scanningPorts: "正在扫描端口",
    openPorts: "开放的端口",
    noOpenPorts: "未发现开放的端口",
    scanFailed: "扫描失败",
    invalidIPFormat: "IP地址格式不正确，请输入有效的IPv4地址",
    startingPortTip: "请输入起始端口",
    endingPortTip: "请输入结束端口",
    endingPortError: "结束端口必须大于起始端口",
    ipSearch: "公网IP查询",
    ipSearchDescription: "查询IP地址的地理位置和运营商信息",
    ipTip: "请输入公网IP地址",
    yourCurrentInfo: "您的当前信息",
    detected: "已检测",
    detecting: "检测中",
    macSearch: "MAC地址查询",
    macSearchDescription: "查询MAC地址对应的厂商信息",
    macAddress: "MAC地址",
    searchResults: "查询结果",
    searching: "查询中",
    query: "查询",
    clear: "清空",
    currentIp: "你的公网IP地址",
    currentLocation: "你的位置",
    flag: "旗帜",
    country: "国家",
    city: "城市",
    isp: "运营商",
    macOrganization: "MAC地址厂商",
    macOrganizationAddress: "厂商地址",
    macTip: "请输入完整的 MAC 地址",
    macFormat: "MAC 地址格式为 XX:XX:XX:XX:XX:XX",
    macInvalid: "无效的 MAC 地址",
    invalidHost: "请输入有效的主机地址",
    loadFailed: "加载失败",
    deviceIdRequired: "设备ID不能为空",
    operationFailed: "操作失败",
    operationSuccess: "操作成功",
    week1: "星期一",
    week2: "星期二",
    week3: "星期三",
    week4: "星期四",
    week5: "星期五",
    week6: "星期六",
    week0: "星期日",
    open: "打开",
    close: "关闭",
    adaptiveMode: "自适应模式",
    enforcementMode: "强制模式",
    enforcementModeTip: "强制模式下，设备将按照规则进行上网控制",
    adaptiveModeTip: "自适应模式下，设备将按照规则进行上网控制",
    disconnect: "断开",
    TenHalf: "10M/半双工",
    TenFull: "10M/全双工",
    HundredHalf: "100M/半双工",
    HundredFull: "100M/全双工",
    ThousandFull: "1000M/全双工",
    TwoThousandFiveFull: "2500M/全双工",
    FiveThousandFull: "5000M/全双工",
    TenThousandFull: "10000M/全双工",
    connected: "已连接",
    disconnected: "未连接",
    powerOn: "已供电",
    powerOff: "未供电",
    unicast: "单播",
    broadcast: "广播",
    multicast: "多播",
    speedUnicast: "单播(速度)",
    speedBroadcast: "广播(速度)",
    speedMulticast: "多播(速度)",
    selectAll: "全选",
    selectedPorts: "已选端口",
    alias: "别名",
    version: "版本",
    group: "分组",
    downNegotiationRate: "下行协商速率",
    upNegotiationRate: "上行协商速率",
    latency: "延迟",
    ExpandDiagram: "展开图示",
    CollapseDiagram: "收起图示",
    operationFail: "操作失败",
    noUpdates: "暂无更新",
    discoverNewVer: "发现新版本",
    updateNow: "立即更新",
    updateLater: "稍后更新",
    upgradeFailed: "更新失败",
    downloadingUpdate: "正在下载更新",
    upgrading: "正在升级",
    updateSuccess: "更新成功",
    fetchingStatusError: "获取状态失败",
    filterTip: "输入关键字进行过滤",
    receivedBytes: "接收",
    transmittedBytes: "发送",
    trafficWeeklyReport: "周流量报表",
    creationTime: "创建时间",
    module: "模块",
    subModule: "子模块",
    message: "消息",
    todo: "待办",
    operationLog: "操作日志",
    title: "标题",
    markAsRead: "标记已读",
    markAllAsRead: "标记全部已读",
    readIdentifier: "已读标识",
    read: "已读",
    unread: "未读",
    collapse: "收起",
    expand: "展开",
    notSet: "未设置",
    noData: "暂无数据",
    noDataTip: "暂无数据，请稍后再试",
    modifySuccess: "修改成功",
    modifyFail: "修改失败",
    modifyBindingSuccess: "修改绑定成功",
    modifyBindingFail: "修改绑定失败",
    unbind: "解绑",
    bindingSuccess: "绑定成功",
    bindingFail: "绑定失败",
    unbindSuccess: "解绑成功",
    unbindFail: "解绑失败",
    unbindConfirm: "是否解绑？",
    pingTest: "Ping测试",
    time: "时间",
    status: "状态",
    startingPortMax: "起始端口最大值不能超过65535",
    startingPortMin: "起始端口最小值不能低于1",
    endingPortMax: "结束端口最大值不能超过65535",
    endingPortMin: "结束端口最小值不能低于1",
    notice: "通知",
    noNotice: "暂无通知",
    noMessage: "暂无消息",
    noTodo: "暂无待办",
    viewAll: "查看全部",
    feedbackSuccess: "反馈成功",
    feedbackFail: "反馈失败",
    manage: "管理",
    deviceStatus: "设备状态",
    pleaseEnter: "请输入",
    pleaseSelect: "请选择",
    pleaseInput: "请输入",
    to: "至",
    projectList: "项目列表",
    onlineTerminal: "在线终端",
    aPManage: "AP管理",
    remoteNetwork: "异地组网",
    remoteNetworkTopology: "异地组网拓扑",
    deleteRemoteNetwork: "删除异地组网",
    deleteRemoteNetworkTip: "确认删除异地组网？",
    deleteRemoteNetworkSuccess: "删除异地组网成功",
    deleteRemoteNetworkFail: "删除异地组网失败",
    selectRemoteNetworkTip: "请选择异地组网",
    remoteNetworkDevices: "组网设备",
    networkType: "网络类型",
    mappingIP: "映射IP",
    allowedAccessIPSegment: "允许访问IP段",
    segmentGenerationMode: "网段生成模式",
    generateAutomatically: "自动生成",
    customization: "自定义",
    onLine: "在线",
    offLine: "离线",
    all: "全部",
    apiNotDefined: "API未定义",
    messageCenter: "消息中心",
    refreshSuccess: "刷新成功",
    refreshing: "正在刷新...",
    bridgeHeightCalculator: "网桥高度计算器",
    bridgeHeightCalculatorDescription: "计算网桥最小安装高度",
    frequencyBandSelection: "频段选择",
    distance: "距离",
    distanceAPtoClientBridge: "AP端到客户端网桥的直线距离",
    distanceObstacleToClientBridge: "最高障碍物到客户端网桥的直线距离(d1)",
    obstacleHeight: "障碍物高度(h)",
    minimumHeightOfBridge: "网桥最小安装高度(h)",
    bridgeHeightCalculatorTip: "网桥高度计算结果仅供参考，实际安装高度请以实际情况为准",
    calculating: "计算中...",
    calculateResult: "计算结果",
    bridgeHeightResult: "网桥最小安装高度",
    meters: "米",
    enterDistance: "请输入距离",
    selectFrequencyBand: "请选择频段",
    enterObstacleHeight: "请输入障碍物高度",
    calculationTip: "计算公式：h' = h + 0.6 × r × √(d × d1 / (d - d1))",
    whereFormula:
      "其中：h'为网桥最小安装高度，h为障碍物高度，r为菲涅尔系数，d为AP到客户端网桥的距离，d1为障碍物到客户端网桥的距离",
    feedbackLengthTip: "反馈内容不能超过1000字",
    settings: "设置",
    limitMax2000: "分配IP数量不能超过2000",
    startIpRange: "起始IP取值范围为2-254",
    requestTimeout: "请求超时，请稍后再试！",
    pleaseSelectBeginEndTime: "请选择开始时间和结束时间",
    allowedIPsMinTip: "至少保留一个IP段",
    submitSuccess: "提交成功",
    rateRangeTip: "请输入0-1000之间的整数"
  },
  home: {
    welcome: "欢迎使用",
    goodMorning: "早上好",
    goodNoon: "中午好",
    goodAfternoon: "下午好",
    goodEvening: "晚上好",
    goodPredawn: "凌晨好"
  },
  tabs: {
    refresh: "刷新",
    maximize: "最大化",
    closeCurrent: "关闭当前",
    closeLeft: "关闭左侧",
    closeRight: "关闭右侧",
    closeOther: "关闭其它",
    closeAll: "关闭所有"
  },
  table: {
    search: "搜索",
    reset: "重置",
    columnSettings: "列设置",
    columnName: "列名",
    columnVisible: "显示",
    noColumnConfig: "没有配置列信息",
    sortable: "排序"
  },
  fontSize: {
    default: "默认",
    large: "大型",
    small: "小型"
  },
  header: {
    componentSize: "组件大小",
    language: "国际化",
    theme: "全局主题",
    layoutConfig: "布局设置",
    layout: "布局样式",
    sideBarColorInversion: "侧边栏反转色",
    sideBarColorInversionTip: "侧边栏颜色变为深色模式",
    headerColorInversion: "头部反转色",
    headerColorInversionTip: "头部颜色变为深色模式",
    themeColor: "主题颜色",
    primary: "primary",
    darkMode: "暗黑模式",
    lightMode: "明亮模式",
    toggleDarkMode: "切换暗黑模式",
    toggleLightMode: "切换明亮模式",
    greyMode: "灰色模式",
    weakMode: "色弱模式",
    fullScreen: "全屏",
    exitFullScreen: "退出全屏",
    personalData: "个人信息",
    changePassword: "修改密码",
    login: "登录",
    logout: "退出登录",
    logoutSuccess: "退出登录成功",
    welcomeLogin: "欢迎登录",
    confirmLogout: "确认退出登录吗？",
    warmRemind: "温馨提示",
    interfaceSetting: "界面设置",
    menuCollapse: "菜单折叠",
    menuAccordion: "菜单手风琴",
    watermark: "水印",
    watermarkTip: "开启水印后，需要刷新页面才能生效",
    breadcrumb: "面包屑",
    breadcrumbIcon: "面包屑图标",
    tabs: "标签栏",
    tabsIcon: "标签栏图标",
    footer: "页脚",
    transverse: "横向",
    vertical: "纵向",
    classic: "经典",
    columns: "分栏",
    searchTip: "菜单搜索:支持菜单名称/路径",
    noMenu: "暂无菜单"
  },
  project: {
    add: "添加",
    addProject: "添加项目",
    editProject: "编辑项目",
    deleteProject: "删除项目",
    edit: "编辑",
    delete: "删除",
    search: "搜索",
    reset: "重置",
    export: "导出",
    batchDelete: "批量删除",
    batchExport: "批量导出",
    batchImport: "批量导入",
    import: "导入",
    importTip: "请先下载模板，再进行导入",
    importSuccess: "导入成功",
    importFail: "导入失败",
    importError: "导入错误",
    importErrorTip: "请检查导入文件是否正确",
    importErrorDetail: "错误详情",
    importErrorDetailTip: "请根据错误提示进行",
    quantity: "数量",
    unit: "台",
    projectNameLabel: "项目名称",
    projectNamePlaceholder: "请输入项目名称",
    project: "项目",
    title: "项目目录",
    selectDeleteProject: "请选择要删除的项目",
    deleteSuccess: "项目已删除",
    deleteCancel: "删除操作已取消",
    deleteConfirm: "您确定要删除该项目吗？",
    deleteWarning: "警告",
    deleteButton: "删除",
    cancelButton: "取消",
    deviceTypeAdd: "添加设备类型",
    deviceTypeEdit: "编辑设备类型",
    projectNameMaxLength: "项目名称不能超过50个字符"
  },
  device: {
    unnamed: "未命名",
    deviceName: "设备名称",
    deviceNamePlaceholder: "请输入设备名称",
    nameRequired: "请输入名称",
    nameMaxLength: "名称长度不能超过32个字符",
    deviceGroup: "设备分组",
    deviceType: "设备类型",
    device: "设备",
    type: "类型",
    typePlaceholder: "请选择设备类型",
    model: "型号",
    modelPlaceholder: "请输入设备型号",
    deviceDetail: "设备详情",
    deviceStatistics: "设备统计",
    deviceList: "设备列表",
    deviceTypeList: "设备类型列表",
    deviceGroupList: "设备分组列表",
    deviceGroupAdd: "添加设备分组",
    deviceTypeAdd: "添加设备类型",
    deviceTypeEdit: "编辑设备类型",
    bootTime: "开机时长",
    mac: "MAC",
    macPlaceholder: "请输入MAC地址",
    ip: "IP地址",
    netmask: "子网掩码",
    online: "在线",
    offline: "离线",
    gawa: "网关",
    ipPlaceholder: "请输入IP地址",
    status: "状态",
    onlineStatusTip: "在线状态",
    connectedStatusTip: "连接状态",
    networkStatusTip: "网络状态",
    deviceId: "设备ID",
    deviceIdPlaceholder: "请输入设备ID",
    deviceIdTooltip: "设备的出厂序列号（SN），通常位于设备外壳或产品包装上。",
    password: "密码",
    passwordPlaceholder: "请输入密码",
    passwordTooltip: "设备的初始管理密码，通常位于设备外壳或产品包装上。如果已修改，请输入修改后的密码。",
    setNewPasswordSuccess: "设置新密码成功",
    setNewPasswordFail: "设置新密码失败",
    bind: "绑定",
    configuration: "配置",
    deviceInfo: "设备信息",
    networkSettings: "网络设置",
    connectionTypeTip: "连接方式",
    lightingSettings: "LED指示灯",
    securitySettings: "安全设置",
    portSettings: "端口配置",
    portSwitch: "端口开关",
    PoeSettings: "PoE设置",
    VlanSettings: "VLAN配置",
    StormSetting: "风暴抑制",
    IsolationControl: "隔离控制",
    QosSettings: "QoS设置",
    portExtend: "超距开关",
    adaptive: "自适应",
    flowControl: "流量控制",
    speedDuplex: "传输速率/方式",
    poeSwitch: "PoE开关",
    poeClass: "PoE分类",
    portPower: "端口功率",
    poeWatchDog: "看门狗",
    PoeWatchDogTime: "触发时间",
    portVlan: "端口VLAN",
    pvidPlaceholder: "输入范围1-4094",
    permitVlanPlaceholder: '输入范围1-4094，多个使用","隔开',
    trafficType: "流量类型",
    portName: "端口名称",
    portStatus: "端口状态",
    portPowerLimit: "功率/限制",
    portRate: "端口速率",
    systemSettings: "设备管理",
    wifiSettings: "WiFi设置",
    DhcpSettings: "DHCP设置",
    lanSettings: "LAN设置",
    dnsSettings: "DNS设置",
    timeSwitch: "定时开关",
    timingTime: "定时时间",
    timingTimePlaceholder: "请选择定时时间",
    startupTime: "打开WIFI",
    closingTime: "关闭WIFI",
    wifi24GHz: "2.4G WI-FI",
    wifi5GHz: "5G WIFI",
    guestWifi: "访客WIFI",
    lanIp: "LAN IP地址",
    lanNetmask: "LAN子网掩码",
    wanIp: "WAN IP地址",
    name: "名称",
    namePlaceholder: "请输入名称",
    hidden: "隐藏WI-FI",
    encryptionMethod: "加密方式",
    key: "密码",
    keyLengthTip: "长度需为8-64位",
    txpower: "发射功率",
    channel: "信道",
    rate: "限速",
    wifiTime: "有效时间",
    startIp: "起始IP",
    startIpPlaceholder: "请输入起始IP地址的最后一部分",
    allocateNumber: "分配IP数量",
    allocateNumberPlaceholder: "请输入分配IP数量",
    dnsEnabled: "DNS获取方式",
    dns1: "首选DNS",
    dns1Placeholder: "请输入首选DNS",
    dns2: "备选DNS",
    dns2Placeholder: "请输入备选DNS",
    dnsModify: "自定义",
    dnsAuto: "自动",
    open: "开启",
    close: "关闭",
    monday: "星期一",
    tuesday: "星期二",
    wednesday: "星期三",
    thursday: "星期四",
    friday: "星期五",
    saturday: "星期六",
    sunday: "星期日",
    everyday: "每天",
    encryption: "加密",
    encryptionNone: "不加密",
    securityManager: "安全管理",
    managePassword: "管理密码",
    managePasswordPlaceholder: "请输入5-64位安全密码",
    oldPassword: "旧密码",
    newPassword: "新密码",
    confirmPassword: "确认密码",
    confirmPasswordPlaceholder: "请再次输入密码",
    unlocking: "不锁定",
    timeLocking: "定时锁定",
    timeLockingPlaceholder: "请选择锁定模式",
    alwaysLock: "一直锁定",
    unknown: "未知模式",
    locking: "锁定",
    lockingTime: "锁定时间",
    lockingTimePlaceholder: "请选择锁定时间",
    lockMode: "锁定模式",
    minute: "分钟",
    oneMinute: "1分钟",
    twoMinutes: "2分钟",
    fiveMinutes: "5分钟",
    tenMinutes: "10分钟",
    fifteenMinutes: "15分钟",
    thirtyMinutes: "30分钟",
    oneHour: "1小时",
    hour: "小时",
    day: "天",
    week: "周",
    month: "月",
    year: "年",
    second: "秒",
    upgradeOnline: "在线升级",
    reboot: "立即重启",
    restarting: "重启中",
    restartSuccess: "重启成功",
    restartFail: "重启失败",
    restartingTip: "设备重启中，请勿重复操作",
    timeRestart: "定时重启",
    timeRestartWeek: "重启日期",
    timeRestartWeekPlaceholder: "请选择重启时间",
    timeRestartTime: "重启时间",
    timeRestartTimePlaceholder: "请选择重启时间",
    timeRestartRate: "延迟重启",
    upgrade: "升级设备",
    upgradeSuccess: "升级成功",
    upgradeFail: "升级失败",
    upgradeTip: "设备升级中，请勿重复操作",
    workmode: "工作模式",
    lanInfo: "LAN信息",
    wanInfo: "WAN信息",
    proto: "联网方式",
    ledConfiguration: "LED指示灯",
    ledTimeRange: "关闭时段",
    ledTimeSameError: "开始时间和结束时间不能相同",
    on: "开",
    off: "关",
    timer: "定时",
    beginTime: "开始时间",
    endTime: "结束时间",
    beginTimePlaceholder: "请选择开始时间",
    endTimePlaceholder: "请选择结束时间",
    lightingMode: "灯光模式",
    lightingModePlaceholder: "请选择灯光模式",
    connected: "已联网",
    disconnected: "未联网",
    dhcp: "动态IP",
    static: "静态IP",
    pppoe: "宽带拨号",
    openTimer: "开启定时",
    closeTimer: "关闭定时",
    manageOfflineDeviceTip: "离线设备无法进行管理",
    terminal: "终端",
    bridgeClient: "网桥客户端",
    connectionDuration: "连接时长",
    rssiTip: "信号强度",
    connectionType: "连接方式",
    operate: "操作",
    qosTip: "QoS优先级",
    ap: "AP",
    unbind: "解绑",
    unbindSuccess: "解绑成功",
    unbindFail: "解绑失败",
    unbindTip: "设备解绑中，请勿重复操作",
    selectDeviceTip: "请选择设备",
    rnetStatusConnecting: "正在连接",
    rnetStatusSuccess: "连接成功",
    rnetStatusFail: "连接失败",
    confirmUnbind: "确定解绑吗？",
    configApplyTip: "当前配置将应用于:",
    selectPortTip: "请选择端口",
    commonRate: "常用速率",
    more: "更多...",
    wirelessRateTip: "请输入1-1000的有效值",
    rateRangeTip: "请输入0-1000之间的整数",
    unlimitedSpeed: "不限速",
    forever: "永久",
    lanIpTip: "请输入LAN IP地址",
    lanNetmaskTip: "请输入LAN子网掩码",
    dhcpTip: "请选择DHCP分配模式",
    ipRequired: "请输入IP地址",
    invalidIpFormat: "IP地址格式不正确，请使用类似 ***********/24 的格式",
    invalidMask: "子网掩码必须在0-32之间",
    invalidIpRange: "IP地址范围不正确，每个数字必须在0-255之间",
    customRequired: "请选择网段生成模式",
    apGroup: "AP分组",
    apGroupConfig: "AP分组配置",
    apGroupTip: "请选择AP分组",
    group1: "分组一",
    group2: "分组二",
    group3: "分组三",
    group4: "分组四",
    addDevice: "添加设备",
    setAsExitDevice: "设为出口设备",
    cancelExitDevice: "取消出口设备",
    viewDetails: "查看详情",
    setAsExitDeviceSuccess: "设置出口设备成功",
    cancelExitDeviceSuccess: "取消出口设备成功",
    cannotSetAsExitDevice: "当前设备不能设为出口设备",
    cannotSetAsExitDeviceTip: "当前设备不能设为出口设备",
    setAsExitDeviceTip: "将当前设备设为出口设备",
    cancelExitDeviceTip: "取消当前设备的出口设备设置",
    deviceNotFound: "未找到设备",
    saveToGroupFailed: "保存到分组失败",
    saveToGroupSuccess: "保存到分组成功",
    saveToGroupSkippedMissingInfo: "缺少分组ID或设备ID，跳过保存到分组操作",
    ledTimeRequiredTip: "请填写LED定时的开始和结束时间",
    currentStatus: "当前状态",
    timerOff: "定时关闭",
    ssidRequired: "SSID不能为空",
    startIpRequired: "起始IP不能为空",
    startIpPositiveInt: "起始IP必须为正整数",
    limitRequired: "分配IP数量不能为空",
    limitPositiveInt: "分配IP数量必须为正整数",
    startIpTip: "网络地址的起始分配地址",
    limitTip: "最大地址分配数量",
    invalidMaskFormat: "子网掩码格式不正确，请输入类似 ************* 的格式",
    ledMode: "LED模式",
    ssidMaxLength: "SSID长度不能超过32位",
    keyRuleAscii: "密码只能包含字母、数字和英文符号",
    passwordRequired: "请输入密码"
  },
  terminal: {
    terminalType: {
      phone: "手机",
      computer: "电脑",
      camera: "摄像头",
      unknown: "未知"
    },
    connectType: {
      radio0: "2.4G",
      radio1: "5G",
      guest: "访客网络",
      lan: "网口连接"
    },
    internetStatus: {
      0: "禁止上网",
      1: "允许上网"
    },
    origin: {
      xiaomi: "小米",
      huawei: "华为",
      honor: "荣耀",
      vivo: "vivo",
      oppo: "OPPO",
      zte: "中兴",
      samsung: "三星",
      apple: "苹果",
      unknow: "未知设备"
    },
    upwardLimit: "上行限速/kb",
    downwardLimit: "下行限速/kb",
    upwardLimitPlaceholder: "请输入上行限速(1-128000)",
    downwardLimitPlaceholder: "请输入下行限速(1-128000)",
    upwardCount: "上行统计",
    downwardCount: "下行统计",
    manufacturer: "厂商",
    controlMode: "控制模式",
    controlModePlaceholder: "请选择控制模式",
    allowAccess: "允许访问",
    denyAccess: "禁止访问",
    weekly: "星期",
    onlineTime: "上网时间",
    group0: "分组一",
    group1: "分组二",
    group2: "分组三",
    group3: "分组四",
    pleaseSelectWeek: "请选择上网时间",
    pleaseSelectMode: "请选择控制模式"
  },
  topology: {
    title: "网络拓扑图",
    deviceTopology: "设备拓扑",
    networkTopology: "网络拓扑",
    loadFailed: "拓扑数据加载失败",
    deviceIdRequired: "设备ID不能为空",
    operationFailed: "操作失败",
    operationSuccess: "操作成功",
    noDeviceSelected: "未选择设备",
    noTopologyData: "没有拓扑数据",
    refreshTopology: "刷新拓扑图",
    switchToHorizontal: "横向布局",
    switchToVertical: "纵向布局",
    resetZoom: "原始大小",
    legend: "图例",
    deviceInfo: {
      title: "设备信息",
      name: "设备名称",
      type: "类型",
      serialNumber: "序列号",
      ip: "IP地址",
      mac: "MAC地址",
      connectionType: "连接方式",
      cableConnection: "有线连接",
      radioConnection: "无线连接"
    },
    deviceName: "设备名称",
    deviceType: "设备类型",
    deviceStatus: "设备状态",
    deviceModel: "设备型号",
    deviceVersion: "设备版本",
    deviceMac: "MAC地址",
    deviceIp: "IP地址",
    online: "在线",
    offline: "离线",
    unknown: "未知",
    router: "路由器",
    switch: "交换机",
    bridge: "网桥",
    repeater: "中继器",
    ap: "AP",
    terminal: "终端",
    editName: "编辑名称",
    saveName: "保存名称",
    cancelEdit: "取消编辑",
    deleteBridgeClient: "删除网桥客户端",
    confirmDeleteBridgeClient: "确定要删除该网桥客户端吗？",
    clientInfo: "客户端信息",
    clientName: "客户端名称",
    clientType: "客户端类型",
    clientStatus: "客户端状态",
    clientMac: "客户端MAC地址",
    clientIp: "客户端IP地址",
    signalStrength: "信号强度",
    transmitRate: "传输速率",
    receiveRate: "接收速率",
    latency: "延迟",
    connectionTime: "连接时间",
    // 拓扑图处理相关
    processDeviceRelation: "处理设备关系",
    specialPort: "特殊端口",
    raPort: "RA端口",
    deviceRelation: "设备关系",
    mainDevice: "主设备",
    relatedDevice: "相关设备",
    addAsChild: "添加为子节点",
    selectGroup: "请选择分组",
    internetNode: "互联网节点",
    wanPort: "WAN端口",
    lanPort: "LAN端口",
    gePort: "GE端口",
    fePort: "FE端口",
    raPortSingle: "RA单数字端口",
    racliPort: "RACLI端口",
    uplink: "上联口",
    downlink: "下联口",
    dport: "目标端口",
    sport: "源端口"
  },

  network: {
    connectionType: "连接类型",
    wiredConnection: "有线连接",
    wirelessConnection: "无线连接"
  },

  footer: {
    beianNumber: "粤ICP备2023072379号",
    beianLink: "ICP备案查询",
    copyright: "2023-2025 © 深圳市众通源科技发展有限公司版权所有",
    allRightsReserved: "保留所有权利"
  },

  guide: {
    title: "用户指南",
    startGuide: "开始指南",
    nextStep: "下一步",
    prevStep: "上一步",
    finish: "完成",
    skip: "跳过",
    close: "关闭",
    welcome: {
      title: "欢迎使用网络管理系统",
      description: "让我们通过简单的指南来了解系统的主要功能"
    },
    navigation: {
      sidebar: {
        title: "侧边栏控制",
        description: "点击此按钮可以折叠或展开左侧菜单栏，节省屏幕空间或快速访问功能菜单"
      },
      breadcrumb: {
        title: "面包屑导航",
        description: "显示当前页面的位置路径，点击任意层级可快速返回上级页面"
      },
      projectTree: {
        title: "项目选择器",
        description: "这里显示所有可用的项目。点击下拉箭头选择不同项目，右侧按钮可以添加新项目或删除当前项目"
      }
    },
    tabs: {
      topology: {
        title: "拓扑图标签",
        description: "切换到网络拓扑图视图，可视化查看网络设备的连接关系"
      },
      project: {
        title: "项目目录标签",
        description: "切换到项目设备列表视图，管理项目中的所有设备"
      }
    },
    topology: {
      canvas: {
        title: "网络拓扑图",
        description: "这里显示网络设备的拓扑结构。您可以点击节点查看设备信息，拖拽移动视图，滚轮缩放"
      },
      controls: {
        title: "拓扑控制按钮",
        description: "使用这些按钮可以刷新拓扑图、切换布局方向、重置缩放等"
      },
      nodeClick: {
        title: "设备节点",
        description: "点击设备节点可以查看详细信息，设置出口设备，或查看设备配置"
      }
    },
    devices: {
      list: {
        title: "设备列表",
        description: "这里显示项目中的所有设备，您可以查看设备状态、绑定新设备或配置现有设备"
      },
      actions: {
        title: "设备操作",
        description: "使用这些按钮可以查看设备详情、编辑设备信息或进行设备配置"
      }
    },
    header: {
      language: {
        title: "语言切换",
        description: "点击这里可以切换系统语言（中文/英文）"
      },
      theme: {
        title: "主题设置",
        description: "点击这里可以自定义系统主题、颜色和布局"
      },
      darkMode: {
        title: "暗黑模式",
        description: "切换明亮模式和暗黑模式"
      },
      fullscreen: {
        title: "全屏模式",
        description: "进入或退出全屏模式"
      },
      guide: {
        title: "用户指南",
        description: "随时点击这里重新查看系统使用指南"
      }
    }
  },

  error: {
    unauthorizedTip: "未授权，请重新登录！", // 401
    badRequestTip: "请求失败，请稍后再试！", // 400
    requestTimeoutTip: "请求超时，请稍后再试！", // 408
    networkErrorTip: "网络错误，请稍后再试！", // 503
    loginExpiredTip: "登录已过期，请重新登录！", // 401
    loginFailedTip: "登录失败，请稍后再试！", // 403
    forbiddenTip: "无权限访问，请联系管理员！", // 403
    resourceNotFoundTip: "资源未找到，请稍后再试！", // 404
    methodNotAllowedTip: "请求方式错误！", // 405
    serverErrorTip: "服务器错误，请稍后再试！", // 500
    badGatewayTip: "网关错误，请稍后再试！", // 502
    serviceUnavailableTip: "服务不可用，请稍后再试！", // 503
    tooManyRequestsTip: "请求过多，请稍后再试！", // 429
    unknownErrorTip: "未知错误，请稍后再试！", // 500
    gatewayTimeoutTip: "网关超时，请稍后再试！", // 504
    conflictTip: "请求冲突，请稍后再试！" // 409
  }
};
