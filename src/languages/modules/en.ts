export default {
  user: {
    userName: "Username",
    userNameTip: "Please enter username",
    password: "Password",
    passwordTip: "Please enter password",
    emailOrPhone: "Email/Phone",
    emailOrPhoneTip: "Please enter email or phone number",
    emailOrPhoneErrorTip: "Invalid email/phone number format",
    phoneFormatError: "Invalid phone number format",
    phoneErrorTip: "Invalid phone number format",
    code: "Code",
    codeTip: "Please enter verification code",
    codeRuleTip: "Verification code must be 4 digits",
    confirmPassword: "Confirm Password",
    confirmPasswordTip: "Please confirm password",
    passwordRuleTip: "Password must contain letters and numbers, and be at least 8 characters",
    passwordNotMatch: "The two passwords do not match",
    nickname: "Nickname",
    inputNickname: "Please enter nickname",
    nicknameTip: "Please enter nickname",
    accountLogin: "Account Login",
    codeLogin: "Code Login",
    nicknameRuleTip: "Nickname cannot exceed 50 characters",
    personalInfo: "Personal Information",
    basicInfo: "Basic Information",
    noAccount: "Don't have an account?",
    registerNow: "Register Now",
    register: "Register",
    sendCode: "Send Code",
    sendCodeFail: "Failed to send verification code",
    codeSent: "Verification code has been sent",
    codeValidTime: "Code valid for 3 minutes",
    resendCodeTime: "Resend({time}s)",
    registerSuccess: "Registration successful",
    registerFail: "Registration failed",
    setNewPassword: "Set New Password",
    setNewPasswordTip: "Please enter new password",
    phone: "Phone",
    phoneTip: "Please enter phone number",
    notCurrentUserPhone: "Not the phone bound to the current user",
    email: "Email",
    newEmail: "New Email",
    emailTip: "Please enter email",
    notCurrentUserEmail: "Not the email bound to the current user",
    deleteAccount: "Delete Account",
    deleteAccountTip: "Are you sure you want to delete your account?",
    deleteAccountSuccess: "Account deleted successfully",
    verifyIdentity: "Verify Identity",
    setNewEmail: "Set New Email",
    setPassword: "Set Password",
    setNewEmailTip: "Please enter new email",
    setNewEmailSuccess: "New email set successfully",
    setNewPhone: "Set New Phone",
    setNewPhoneTip: "Please enter new phone number",
    setNewPhoneSuccess: "New phone number set successfully",
    done: "Done",
    resendCode: "Resend Code",
    sendCodeSuccess: "Verification code sent successfully",
    confirmRegist: "Do you want to go to the registration page?",
    userExist: "User already exists",
    userNotExist: "User does not exist",
    forgetPassword: "Forgot Password",
    resetPassword: "Reset Password",
    emailBeenBind: "Email has been bound",
    phoneBeenBind: "Phone number has been bound",
    emailNotEmpty: "Email cannot be empty",
    codeNotEmpty: "Verification code cannot be empty",
    emailSame: "New email is the same as the original email",
    codeFormatTip: "Invalid code format"
  },
  common: {
    refresh: "Refresh",
    deviceStatistics: "Device Statistics",
    reset: "Reset",
    confirm: "Confirm",
    cancel: "Cancel",
    save: "Save",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    search: "Search",
    modify: "Modify",
    binding: "Binding",
    modifyBinding: "Modify Binding",
    feedback: "Feedback",
    feedbackTip: "Please enter your feedback",
    contact: "Contact",
    contactTip: "Please leave your contact information (optional)",
    userAgreement: "User Agreement",
    privacyPolicy: "Privacy Policy",
    quickLinks: "Quick Links",
    resetPassword: "Reset Password",
    resetPasswordSuccess: "Reset password successful",
    resetPasswordFail: "Reset password failed",
    resetPasswordConfirm: "Are you sure to reset the password?",
    blackList: "Black List",
    addBlackList: "Add to Black List",
    internet: "Internet",
    view: "View",
    deleteConfirm: "Are you sure to delete?",
    deleteSuccess: "Delete successful",
    deleteFail: "Delete failed",
    deleteFailTip: "Delete failed",
    yes: "Yes",
    no: "No",
    networkLimit: "Network Limit",
    internetControl: "Internet Control",
    invalidHostFormat: "Invalid host format, please enter a valid IP address or domain name",
    pingHost: "Please enter host address",
    pingCount: "Please enter ping count",
    pingInterval: "Please enter ping interval",
    pingCountError: "Ping count must be greater than 0",
    pingIntervalError: "Ping interval must be greater than 100ms",
    startPing: "Start Ping",
    sequenceNumber: "Sequence No.",
    pingTime: "Time",
    pingResult: "Result",
    pingDelay: "Delay",
    pingTTL: "TTL",
    packetsSent: "Packets Sent",
    packetsReceived: "Packets Received",
    packetLoss: "Packet Loss",
    averageDelay: "Average Delay",
    success: "Success",
    failed: "Failed",
    pingTestFailed: "Ping test failed",
    invalidDataFormat: "Invalid data format",
    invalidDelayFormat: "Invalid delay format",
    portScan: "Port Scan",
    portScanDescription: "Scan ports on a specified host to discover open ports and services",
    ipAddress: "IP Address",
    portRange: "Port Range",
    startingPort: "Starting Port",
    endingPort: "Ending Port",
    startingScan: "Start Scan",
    scanResults: "Scan Results",
    portsFound: "ports found",
    scanning: "Scanning",
    scanningPorts: "Scanning ports",
    openPorts: "Open Ports",
    noOpenPorts: "No open ports found",
    scanFailed: "Scan failed",
    invalidIPFormat: "Invalid IP format, please enter a valid IPv4 address",
    startingPortTip: "Please enter starting port",
    endingPortTip: "Please enter ending port",
    endingPortError: "Ending port must be greater than starting port",
    ipSearch: "Public IP Query",
    ipSearchDescription: "Query geographic location and ISP information for IP addresses",
    ipTip: "Please enter your public IP address",
    yourCurrentInfo: "Your Current Information",
    detected: "Detected",
    detecting: "Detecting",
    macSearch: "MAC Address Query",
    macSearchDescription: "Query manufacturer information for MAC addresses",
    macAddress: "MAC Address",
    searchResults: "Search Results",
    searching: "Searching",
    query: "Query",
    clear: "Clear",
    currentIp: "Your Public IP Address",
    currentLocation: "Your Location",
    flag: "Flag",
    country: "Country",
    city: "City",
    isp: "ISP",
    macOrganization: "MAC Organization",
    macOrganizationAddress: "MAC Organization Address",
    macTip: "Please enter a complete MAC address",
    macFormat: "MAC address format should be XX:XX:XX:XX:XX:XX",
    macInvalid: "Invalid MAC address",
    invalidHost: "Please enter a valid host address",
    loadFailed: "Load failed",
    deviceIdRequired: "Device ID is required",
    operationFailed: "Operation failed",
    operationSuccess: "Operation successful",
    week1: "Monday",
    week2: "Tuesday",
    week3: "Wednesday",
    week4: "Thursday",
    week5: "Friday",
    week6: "Saturday",
    week0: "Sunday",
    open: "Open",
    close: "Close",
    adaptiveMode: "Adaptive Mode",
    enforcementMode: "Enforcement Mode",
    enforcementModeTip: "In enforcement mode, devices will follow the rules for internet control",
    adaptiveModeTip: "In adaptive mode, devices will follow the rules for internet control",
    disconnect: "Disconnect",
    TenHalf: "10M/Half-Duplex",
    TenFull: "10M/Full-Duplex",
    HundredHalf: "100M/Half-Duplex",
    HundredFull: "100M/Full-Duplex",
    ThousandFull: "1000M/Full-Duplex",
    TwoThousandFiveFull: "2500M/Full-Duplex",
    FiveThousandFull: "5000M/Full-Duplex",
    TenThousandFull: "10000M/Full-Duplex",
    connected: "Connected",
    disconnected: "Disconnected",
    powerOn: "Power On",
    powerOff: "Power Off",
    unicast: "Unicast",
    broadcast: "Broadcast",
    multicast: "Multicast",
    speedUnicast: "Speed(Unicast)",
    speedBroadcast: "Speed(Broadcast)",
    speedMulticast: "Speed(Multicast)",
    selectAll: "Select All",
    selectedPorts: "Selected Ports",
    alias: "Alias",
    version: "Version",
    group: "Group",
    downNegotiationRate: "Down Negotiation Rate",
    upNegotiationRate: "Up Negotiation Rate",
    latency: "Latency",
    ExpandDiagram: "Expand Diagram",
    CollapseDiagram: "Collapse Diagram",
    noUpdates: "No Updates",
    discoverNewVer: "New Version Available",
    updateNow: "Update Now",
    updateLater: "Update Later",
    upgradeFailed: "Update Failed",
    downloadingUpdate: "Downloading Update",
    upgrading: "Upgrading",
    updateSuccess: "Update Success",
    fetchingStatusError: "Failed to Fetch Status",
    filterTip: "Enter keywords to filter",
    receivedBytes: "Received Bytes",
    transmittedBytes: "Transmitted Bytes",
    trafficWeeklyReport: "Weekly Traffic Report",
    creationTime: "Creation Time",
    module: "Module",
    subModule: "Sub-module",
    message: "Message",
    todo: "Todo",
    operationLog: "Operation Log",
    title: "Title",
    markAsRead: "Mark as Read",
    markAllAsRead: "Mark All as Read",
    readIdentifier: "Read Identifier",
    read: "Read",
    unread: "Unread",
    collapse: "Collapse",
    expand: "Expand",
    notSet: "Not Set",
    noData: "No Data",
    noDataTip: "No data available, please try again later",
    modifySuccess: "Modified Successfully",
    modifyFail: "Modification Failed",
    modifyBindingSuccess: "Binding Modified Successfully",
    modifyBindingFail: "Binding Modification Failed",
    unbind: "Unbind",
    bindingSuccess: "Binding Successful",
    bindingFail: "Binding Failed",
    unbindSuccess: "Unbound Successfully",
    unbindFail: "Unbinding Failed",
    unbindConfirm: "Are you sure you want to unbind?",
    pingTest: "Ping Test",
    time: "Time",
    status: "Status",
    startingPortMax: "Maximum value is 65535",
    startingPortMin: "Minimum value is 1",
    endingPortMax: "Maximum value is 65535",
    endingPortMin: "Minimum value is 1",
    notice: "Notice",
    noNotice: "No notices",
    noMessage: "No messages",
    noTodo: "No todos",
    viewAll: "View All",
    feedbackSuccess: "Feedback Submitted Successfully",
    messageCenter: "Message Center",
    refreshSuccess: "Refresh Success",
    refreshing: "Refreshing...",
    feedbackFail: "Feedback Submission Failed",
    manage: "Manage",
    deviceStatus: "Device Status",
    pleaseEnter: "Please enter",
    pleaseSelect: "Please select",
    to: "to",
    projectList: "Project List",
    onlineTerminal: "Online Terminal",
    aPManage: "AP Management",
    remoteNetwork: "Remote Network",
    remoteNetworkTopology: "Remote Network Topology",
    deleteRemoteNetwork: "Delete Remote Network",
    deleteRemoteNetworkTip: "Are you sure you want to delete the remote network?",
    deleteRemoteNetworkSuccess: "Remote network deleted successfully",
    deleteRemoteNetworkFail: "Failed to delete remote network",
    selectRemoteNetworkTip: "Please select a remote network",
    remoteNetworkDevices: "Remote Network Devices",
    networkType: "Network Type",
    mappingIP: "Mapping IP",
    allowedAccessIPSegment: "Allowed Access IP Segments",
    segmentGenerationMode: "Segment Generation Mode",
    generateAutomatically: "Generate Automatically",
    customization: "Customization",
    onLine: "Online",
    offLine: "Offline",
    all: "All",
    apiNotDefined: "API is not defined",
    bridgeHeightCalculator: "Bridge Height Calculator",
    bridgeHeightCalculatorDescription: "Calculate the minimum installation height of the bridge",
    frequencyBandSelection: "Frequency Band Selection",
    distance: "Distance",
    distanceAPtoClientBridge: "AP to Bridge Distance",
    distanceObstacleToClientBridge: "Obstacle to Bridge Distance (d1)",
    obstacleHeight: "Obstacle Height (h)",
    minimumHeightOfBridge: "Minimum Height of Bridge (h')",
    bridgeHeightCalculatorTip: "The calculation result is for reference only. Please adjust according to actual conditions",
    calculating: "Calculating...",
    calculateResult: "Calculation Result",
    bridgeHeightResult: "Minimum Bridge Height",
    meters: "meters",
    enterDistance: "Please enter distance",
    selectFrequencyBand: "Please select frequency band",
    enterObstacleHeight: "Please enter obstacle height",
    calculationTip: "Formula: h' = h + 0.6 × r × √(d × d1 / (d - d1))",
    whereFormula:
      "Where: h' is the minimum bridge height, h is the obstacle height, r is the Fresnel coefficient, d is the distance from AP to client bridge, d1 is the distance from obstacle to client bridge",
    auto: "Auto",
    feedbackLengthTip: "Feedback content cannot exceed 1000 characters",
    ledMode: "LED Mode",
    startIpRange: "Start IP must be between 2 and 254",
    limitMax2000: "Allocation count cannot exceed 2000",
    ssidMaxLength: "SSID length cannot exceed 32 characters",
    requestTimeout: "Request timeout, please try again later!",
    pleaseSelectBeginEndTime: "Please select both Begin Time and End Time",
    allowedIPsMinTip: "At least one IP segment must be retained",
    keyRuleAscii: "Password can only contain letters, numbers and ASCII symbols",
    submitSuccess: "Submit successful",
    rateRangeTip: "Please enter an integer between 0-1000"
  },
  home: {
    welcome: "Welcome",
    goodMorning: "Good morning",
    goodNoon: "Good noon",
    goodAfternoon: "Good afternoon",
    goodEvening: "Good evening",
    goodPredawn: "Good predawn"
  },
  tabs: {
    refresh: "Refresh",
    maximize: "Maximize",
    closeCurrent: "Close current",
    closeLeft: "Close Left",
    closeRight: "Close Right",
    closeOther: "Close other",
    closeAll: "Close All"
  },
  table: {
    search: "Search",
    reset: "Reset",
    columnSettings: "Column Settings",
    columnName: "Column Name",
    columnVisible: "Column Visible",
    noColumnConfig: "No column configuration",
    sortable: "Sortable"
  },
  fontSize: {
    default: "Default",
    large: "Large",
    small: "Small"
  },
  header: {
    componentSize: "Component size",
    language: "Language",
    theme: "Theme",
    layoutConfig: "Layout Config",
    sideBarColorInversion: "SideBar Color Inversion",
    sideBarColorInversionTip: "SideBar Color changes to dark mode",
    headerColorInversion: "Header Color Inversion",
    headerColorInversionTip: "Header Color changes to dark mode",
    themeColor: "Theme Color",
    layout: "Layout",
    primary: "primary",
    darkMode: "Dark Mode",
    lightMode: "Light Mode",
    toggleDarkMode: "Switch to Dark Mode",
    toggleLightMode: "Switch to Light Mode",
    greyMode: "Grey mode",
    weakMode: "Weak mode",
    fullScreen: "Full Screen",
    exitFullScreen: "Exit Full Screen",
    personalData: "Personal Data",
    changePassword: "Change Password",
    login: "Login",
    logout: "Logout",
    logoutSuccess: "Logout Success",
    welcomeLogin: "Welcome to login",
    confirmLogout: "Are you sure to log out?",
    warmRemind: "Warm reminder",
    interfaceSetting: "Interface Setting",
    menuCollapse: "Menu Collapse",
    menuAccordion: "Menu Accordion",
    watermark: "Watermark",
    breadcrumb: "Breadcrumb",
    breadcrumbIcon: "Breadcrumb Icon",
    tabs: "Tabs",
    tabsIcon: "Tabs Icon",
    footer: "Footer",
    transverse: "Transverse",
    vertical: "Vertical Direction",
    classic: "Classic",
    columns: "Columns",
    searchTip: "Menu search: Support menu name/path",
    noMenu: "No menu"
  },
  project: {
    add: "Add",
    addProject: "Add Project",
    editProject: "Edit Project",
    deleteProject: "Delete Project",
    edit: "Edit",
    delete: "Delete",
    search: "Search",
    reset: "Reset",
    export: "Export",
    batchDelete: "Batch Delete",
    batchExport: "Batch Export",
    batchImport: "Batch Import",
    import: "Import",
    importTip: "Please download the template first, then import",
    importSuccess: "Import Success",
    importFail: "Import Fail",
    importError: "Import Error",
    importErrorTip: "Please check the import file",
    importErrorDetail: "Error Details",
    importErrorDetailTip: "Please according to the error prompt",
    quantity: "Qty",
    unit: "unit",
    projectNameLabel: "Project Name",
    projectNamePlaceholder: "Please enter project name",
    project: "Project",
    title: "Project Directory",
    selectDeleteProject: "Please select a project to delete",
    deleteSuccess: "Project deleted successfully",
    deleteCancel: "Delete operation cancelled",
    deleteConfirm: "Are you sure you want to delete this project?",
    deleteWarning: "Warning",
    deleteButton: "Delete",
    cancelButton: "Cancel",
    deviceTypeAdd: "Add Device Type",
    deviceTypeEdit: "Edit Device Type",
    projectNameMaxLength: "Project name cannot exceed 50 characters"
  },
  device: {
    unnamed: "Unnamed",
    deviceName: "Device Name",
    deviceNamePlaceholder: "Please enter device name",
    nameRequired: "Please enter name",
    nameMaxLength: "Name length cannot exceed 32 characters",
    deviceGroup: "Device Group",
    deviceType: "Device Type",
    device: "Device",
    type: "Type",
    typePlaceholder: "Please select device type",
    model: "Model",
    modelPlaceholder: "Please enter the model",
    deviceDetail: "Device Detail",
    deviceStatistics: "Device Statistics",
    deviceList: "Device List",
    deviceTypeList: "Device Type List",
    deviceGroupList: "Device Group List",
    deviceGroupAdd: "Add Device Group",
    deviceTypeAdd: "Add Device Type",
    deviceTypeEdit: "Edit Device Type",
    bootTime: "Boot Duration",
    mac: "MAC",
    macPlaceholder: "Please enter MAC",
    ip: "IP",
    netmask: "Netmask",
    online: "Online",
    offline: "Offline",
    gawa: "Gateway",
    ipPlaceholder: "Please enter IP",
    status: "Status",
    onlineStatusTip: "Online Status",
    connectedStatusTip: "Connected Status",
    networkStatusTip: "Network Status",
    connectionTypeTip: "Connection Type",
    deviceId: "Device ID",
    deviceIdPlaceholder: "Please enter the device ID",
    deviceIdTooltip: "The factory serial number (SN) of the device, usually found on the device's casing or packaging.",
    password: "Password",
    passwordPlaceholder: "Please enter the password",
    passwordTooltip:
      "The initial administrator password for the device, typically found on the device's casing or packaging. If you have changed it, please enter the new password.",
    setNewPasswordSuccess: "Set new password successfully",
    setNewPasswordFail: "Set new password failed",
    bind: "Bind",
    configuration: "Configuration",
    deviceInfo: "Device Information",
    networkSettings: "Network Settings",
    lightingSettings: "LED Configuration",
    securitySettings: "Security Settings",
    portSettings: "Port Settings",
    portSwitch: "Port Switch",
    PoeSettings: "PoE Settings",
    VlanSettings: "VLAN Settings",
    StormSetting: "Storm Control",
    IsolationControl: "Isolation Control",
    QosSettings: "QoS Settings",
    portExtend: "Long Distance Switch",
    adaptive: "Adaptive",
    flowControl: "Flow Control",
    speedDuplex: "Speed/Duplex",
    poeSwitch: "PoE Switch",
    poeClass: "PoE Class",
    portPower: "Port Power",
    poeWatchDog: "PoE WatchDog",
    PoeWatchDogTime: "PoE WatchDog Time",
    portVlan: "Port VLAN",
    pvidPlaceholder: "Please enter 1-4094",
    permitVlanPlaceholder: 'Enter range 1-4094, separate multiple values with ","',
    trafficType: "Traffic Type",
    portName: "Port Name",
    portStatus: "Port Status",
    portPowerLimit: "Port Power/Limit",
    portRate: "Port Rate",
    systemSettings: "System Settings",
    wifiSettings: "WiFi Settings",
    DhcpSettings: "DHCP Settings",
    lanSettings: "LAN Settings",
    dnsSettings: "DNS Settings",
    timeSwitch: "Time Switch",
    timingTime: "Timing Time",
    timingTimePlaceholder: "Please select timing time",
    startupTime: "Startup Time",
    closingTime: "Closing Time",
    wifi24GHz: "2.4G WiFi",
    wifi5GHz: "5G WiFi",
    guestWifi: "Guest WiFi",
    lanIp: "LAN IP",
    lanNetmask: "LAN Subnet Mask",
    wanIp: "WAN IP",
    name: "Name",
    namePlaceholder: "Please enter name",
    hidden: "Hidden WiFi",
    encryptionMethod: "Encryption Method",
    key: "Key",
    keyLengthTip: "Length must be 8-64 characters",
    txpower: "Transmission Power",
    channel: "Channel",
    rate: "Rate",
    wifiTime: "Effective Time",
    startIp: "Start IP",
    startIpPlaceholder: "Please enter the last part of the start IP address",
    allocateNumber: "Allocate Number",
    allocateNumberPlaceholder: "Please enter allocate number",
    dnsEnabled: "DNS Enabled",
    dns1: "DNS1",
    dns1Placeholder: "Please enter DNS1",
    dns2: "DNS2",
    dns2Placeholder: "Please enter DNS2",
    dnsModify: "Modify DNS",
    dnsAuto: "Auto",
    open: "On",
    close: "Off",
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday",
    saturday: "Saturday",
    sunday: "Sunday",
    everyday: "Everyday",
    encryption: "Encryption",
    encryptionNone: "None",
    securityManager: "Security Manager",
    managePassword: "Manage Password",
    managePasswordPlaceholder: "Please enter 5-64 bit security password",
    oldPassword: "Old Password",
    newPassword: "New Password",
    confirmPassword: "Confirm Password",
    confirmPasswordPlaceholder: "Please re-enter the password",
    unlocking: "Unlocking",
    timeLocking: "Time Locking",
    timeLockingPlaceholder: "Please select locking mode",
    alwaysLock: "Always Lock",
    unknown: "Unknown",
    locking: "Locking",
    lockingTime: "Locking Time",
    lockingTimePlaceholder: "Please select locking time",
    lockMode: "Lock Mode",
    minute: "Minute",
    oneMinute: "1 Minute",
    twoMinutes: "2 Minutes",
    fiveMinutes: "5 Minutes",
    tenMinutes: "10 Minutes",
    fifteenMinutes: "15 Minutes",
    thirtyMinutes: "30 Minutes",
    oneHour: "1 Hour",
    hour: "Hour",
    day: "Day",
    week: "Week",
    month: "Month",
    year: "Year",
    second: "Second",
    upgradeOnline: "Online Upgrade",
    reboot: "Reboot",
    restarting: "Restarting",
    restartSuccess: "Restart Success",
    restartFail: "Restart Fail",
    restartingTip: "Device is restarting, please do not repeat operation",
    timeRestart: "Timed Restart",
    timeRestartWeek: "Restart Day",
    timeRestartWeekPlaceholder: "Please select restart time",
    timeRestartTime: "Restart Time",
    timeRestartTimePlaceholder: "Please select restart time",
    timeRestartRate: "Delay Restart",
    upgrade: "Upgrade",
    upgradeSuccess: "Upgrade Success",
    upgradeFail: "Upgrade Fail",
    upgradeTip: "Device is upgrading, please do not repeat operation",
    workmode: "Work Mode",
    lanInfo: "LAN Info",
    wanInfo: "WAN Info",
    proto: "Network Mode",
    ledConfiguration: "LED Configuration",
    ledTimeRange: "Off Period",
    ledTimeSameError: "Begin time and end time cannot be the same",
    on: "On",
    off: "Off",
    timer: "Timer",
    beginTime: "Begin Time",
    endTime: "End Time",
    beginTimePlaceholder: "Please select begin time",
    endTimePlaceholder: "Please select end time",
    lightingMode: "Lighting Mode",
    lightingModePlaceholder: "Please select lighting mode",
    connected: "Connected",
    disconnected: "Disconnected",
    dhcp: "Dynamic IP",
    static: "Static IP",
    pppoe: "Broadband Dial-up",
    openTimer: "Open Timer",
    closeTimer: "Close Timer",
    manageOfflineDeviceTip: "Offline device cannot be managed",
    bridgeClient: "Bridge Client",
    terminal: "Terminal",
    connectionDuration: "Connection Duration",
    rssiTip: "Signal Strength",
    connectionType: "Connection Type",
    operate: "Operate",
    qosTip: "QoS Priority",
    ap: "AP",
    unbind: "Unbind",
    unbindSuccess: "Unbind Success",
    unbindFail: "Unbind Fail",
    unbindTip: "Device is unbinding, please do not repeat operation",
    selectDeviceTip: "Please select device",
    rnetStatusConnecting: "Connecting",
    rnetStatusSuccess: "Success",
    rnetStatusFail: "Fail",
    confirmUnbind: "Are you sure you want to unbind?",
    configApplyTip: "The current configuration will be applied to:",
    selectPortTip: "Please select port",
    commonRate: "Common Rate",
    more: "More...",
    wirelessRateTip: "Please enter a valid value of 1-1000",
    rateRangeTip: "Please enter an integer between 0-1000",
    unlimitedSpeed: "Unlimited Speed",
    forever: "Forever",
    lanIpTip: "Please enter LAN IP address",
    lanNetmaskTip: "Please enter LAN Subnet Mask",
    dhcpTip: "Please select DHCP mode",
    ipRequired: "Please enter IP address",
    invalidIpFormat: "Invalid IP address format, please use format like ***********/24",
    invalidMask: "Subnet mask must be between 0-32",
    invalidIpRange: "Invalid IP address range, each number must be between 0-255",
    customRequired: "Please select segment generation mode",
    apGroup: "AP Group",
    apGroupConfig: "AP Group Configuration",
    apGroupTip: "Please select AP group",
    group1: "Group 1",
    group2: "Group 2",
    group3: "Group 3",
    group4: "Group 4",
    addDevice: "Add Device",
    setAsExitDevice: "Set as Exit Device",
    cancelExitDevice: "Cancel Exit Device",
    viewDetails: "View Details",
    setAsExitDeviceSuccess: "Set as exit device successfully",
    cancelExitDeviceSuccess: "Canceled exit device successfully",
    cannotSetAsExitDevice: "This device cannot be set as an exit device",
    cannotSetAsExitDeviceTip: "This device cannot be set as an exit device",
    setAsExitDeviceTip: "Set this device as an exit device",
    cancelExitDeviceTip: "Cancel this device's exit device setting",
    deviceNotFound: "Device not found",
    saveToGroupFailed: "Failed to save to group",
    saveToGroupSuccess: "Saved to group successfully",
    saveToGroupSkippedMissingInfo: "Missing Group ID or Device ID, skipped saving to group",
    ledTimeRequiredTip: "Please fill in the start and end time for LED timer mode.",
    currentStatus: "Current Status",
    timerOff: "Timer Off",
    ssidRequired: "SSID cannot be empty.",
    startIpRequired: "Start IP cannot be empty.",
    startIpPositiveInt: "Start IP must be a positive integer.",
    limitRequired: "Allocation count cannot be empty.",
    limitPositiveInt: "Allocation count must be a positive integer.",
    startIpTip: "Start address for network allocation",
    limitTip: "Maximum number of allocated addresses",
    invalidMaskFormat: "Invalid subnet mask format, please enter like *************",
    passwordRequired: "Please enter password"
  },
  terminal: {
    terminalType: {
      phone: "Mobile Phone",
      computer: "Computer",
      camera: "Camera",
      unknown: "Unknown"
    },
    connectType: {
      radio0: "2.4G WiFi",
      radio1: "5G WiFi",
      guest: "Guest Network",
      lan: "LAN Connection"
    },
    internetStatus: {
      0: "Forbidden Access",
      1: "Allow Access"
    },
    origin: {
      xiaomi: "XiaoMi",
      huawei: "HuaWei",
      honor: "Honor",
      vivo: "Vivo",
      oppo: "OPPO",
      zte: "ZTE",
      samsung: "Samsung",
      apple: "Apple",
      unknow: "Unknown"
    },
    upwardLimit: "Upward Limit(kb)",
    downwardLimit: "Downward Limit(kb)",
    upwardLimitPlaceholder: "Please enter upward limit(1-128000)",
    downwardLimitPlaceholder: "Please enter downward limit(1-128000)",
    upwardCount: "Upward Count",
    downwardCount: "Downward Count",
    manufacturer: "Manufacturer",
    controlMode: "Control Mode",
    controlModePlaceholder: "Please select control mode",
    allowAccess: "Allow Access",
    denyAccess: "Deny Access",
    weekly: "Weekly",
    onlineTime: "Internet Access Period",
    group0: "Group 1",
    group1: "Group 2",
    group2: "Group 3",
    group3: "Group 4",
    pleaseSelectWeek: "Please select Internet Control Time",
    pleaseSelectMode: "Please select Control Mode"
  },
  topology: {
    title: "Network Topology",
    deviceTopology: "Device Topology",
    networkTopology: "Network Topology",
    loadFailed: "Failed to load topology data",
    deviceIdRequired: "Device ID is required",
    operationFailed: "Operation failed",
    operationSuccess: "Operation successful",
    noDeviceSelected: "No device selected",
    noTopologyData: "No topology data available",
    refreshTopology: "Refresh Topology",
    switchToHorizontal: "Horizontal",
    switchToVertical: "Vertical",
    resetZoom: "Reset Zoom",
    legend: "Legend",
    deviceInfo: {
      title: "Device Information",
      name: "Device Name",
      type: "Type",
      serialNumber: "Serial Number",
      ip: "IP Address",
      mac: "MAC Address",
      connectionType: "Connection Type",
      cableConnection: "Wired Connection",
      radioConnection: "Wireless Connection"
    },
    deviceName: "Device Name",
    deviceType: "Device Type",
    deviceStatus: "Device Status",
    deviceModel: "Device Model",
    deviceVersion: "Device Version",
    deviceMac: "MAC Address",
    deviceIp: "IP Address",
    online: "Online",
    offline: "Offline",
    unknown: "Unknown",
    router: "Router",
    switch: "Switch",
    bridge: "Bridge",
    repeater: "Repeater",
    ap: "AP",
    terminal: "Terminal",
    editName: "Edit Name",
    saveName: "Save Name",
    cancelEdit: "Cancel Edit",
    deleteBridgeClient: "Delete Bridge Client",
    confirmDeleteBridgeClient: "Are you sure you want to delete this bridge client?",
    clientInfo: "Client Information",
    clientName: "Client Name",
    clientType: "Client Type",
    clientStatus: "Client Status",
    clientMac: "Client MAC Address",
    clientIp: "Client IP Address",
    signalStrength: "Signal Strength",
    transmitRate: "Transmit Rate",
    receiveRate: "Receive Rate",
    latency: "Latency",
    connectionTime: "Connection Time",
    // Topology processing related
    processDeviceRelation: "Process Device Relation",
    specialPort: "Special Port",
    raPort: "RA Port",
    deviceRelation: "Device Relation",
    mainDevice: "Main Device",
    relatedDevice: "Related Device",
    addAsChild: "Add as Child Node",
    selectGroup: "Please select a group",
    internetNode: "Internet Node",
    wanPort: "WAN Port",
    lanPort: "LAN Port",
    gePort: "GE Port",
    fePort: "FE Port",
    raPortSingle: "RA Single Digit Port",
    racliPort: "RACLI Port",
    uplink: "Uplink",
    downlink: "Downlink",
    dport: "Destination Port",
    sport: "Source Port"
  },
  network: {
    connectionType: "Connection Type",
    wiredConnection: "Wired Connection",
    wirelessConnection: "Wireless Connection"
  },
  footer: {
    beianNumber: "粤ICP备2023072379号",
    beianLink: "ICP Filing Query",
    copyright: "2023-2025 © Shenzhen Hisource Technology Development Co., Ltd. All Rights Reserved",
    allRightsReserved: "All Rights Reserved"
  },
  guide: {
    title: "User Guide",
    startGuide: "Start Guide",
    nextStep: "Next",
    prevStep: "Previous",
    finish: "Finish",
    skip: "Skip",
    close: "Close",
    welcome: {
      title: "Welcome to Network Management System",
      description: "Let's explore the main features of the system through this simple guide"
    },
    navigation: {
      sidebar: {
        title: "Sidebar",
        description: "Click this button to collapse or expand the sidebar menu"
      },
      breadcrumb: {
        title: "Breadcrumb Navigation",
        description: "Shows the current page location path to help you understand where you are"
      },
      projectTree: {
        title: "Project List",
        description: "Select the project you want to manage here. You can add, delete projects, or switch to different projects"
      }
    },
    tabs: {
      topology: {
        title: "Topology Tab",
        description: "Switch to network topology view to visualize the connection relationships between network devices"
      },
      project: {
        title: "Project Directory Tab",
        description: "Switch to project device list view to manage all devices in the project"
      }
    },
    topology: {
      canvas: {
        title: "Network Topology",
        description:
          "This shows the topology structure of network devices. You can click nodes to view device information, drag to move the view, and scroll to zoom"
      },
      controls: {
        title: "Topology Controls",
        description: "Use these buttons to refresh topology, switch layout direction, reset zoom, etc."
      },
      nodeClick: {
        title: "Device Node",
        description: "Click device nodes to view detailed information, set exit devices, or view device configuration"
      }
    },
    devices: {
      list: {
        title: "Device List",
        description:
          "This shows all devices in the project. You can view device status, bind new devices, or configure existing devices"
      },
      actions: {
        title: "Device Actions",
        description: "Use these buttons to view device details, edit device information, or configure devices"
      }
    },
    header: {
      language: {
        title: "Language Switch",
        description: "Click here to switch system language (Chinese/English)"
      },
      theme: {
        title: "Theme Settings",
        description: "Click here to customize system theme, colors, and layout"
      },
      darkMode: {
        title: "Dark Mode",
        description: "Switch between light mode and dark mode"
      },
      fullscreen: {
        title: "Fullscreen Mode",
        description: "Enter or exit fullscreen mode"
      },
      guide: {
        title: "User Guide",
        description: "Click here anytime to review the system usage guide"
      }
    }
  },
  error: {
    badRequestTip: "Bad Request, please try again later.",
    requestTimeoutTip: "Request Timeout, please try again later.",
    networkErrorTip: "Network Error, please try again later.",
    loginExpiredTip: "Login Expired, please login again.",
    loginFailedTip: "Login Failed, please try again later.",
    forbiddenTip: "Access denied. Please contact the administrator.",
    resourceNotFoundTip: "Resource not found. Please try again later.",
    methodNotAllowedTip: "Method not allowed. Please try again later.",
    serverErrorTip: "Server Error, please try again later.",
    badGatewayTip: "Bad Gateway, please try again later.",
    serviceUnavailableTip: "Service Unavailable, please try again later.",
    tooManyRequestsTip: "Too Many Requests, please try again later.",
    unknownErrorTip: "Unknown Error, please try again later.",
    gatewayTimeoutTip: "Gateway Timeout, please try again later.",
    conflictTip: "Request conflict. Please try again later."
  }
};
