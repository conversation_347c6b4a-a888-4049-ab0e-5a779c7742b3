// Custom ECharts type declarations to support additional chart types
import * as echarts from "echarts/core";
import {
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  ScatterSeriesOption,
  RadarSeriesOption,
  GaugeSeriesOption,
  LinesSeriesOption
} from "echarts/charts";

// Add custom series types
declare module "echarts/core" {
  // Add graph series type
  interface GraphSeriesOption {
    type: "graph";
    layout?: "none" | "circular" | "force";
    data?: any[];
    links?: any[];
    categories?: any[];
    roam?: boolean;
    focusNodeAdjacency?: boolean;
    symbolSize?: number | Function;
    label?: any;
    lineStyle?: any;
    emphasis?: any;
    force?: {
      repulsion?: number;
      edgeLength?: number;
      gravity?: number;
      [key: string]: any;
    };
    [key: string]: any;
  }

  // Add tree series type
  interface TreeSeriesOption {
    type: "tree";
    data?: any[];
    layout?: "orthogonal" | "radial";
    orient?: "LR" | "RL" | "TB" | "BT";
    symbolSize?: number | Function;
    label?: any;
    lineStyle?: any;
    emphasis?: any;
    expandAndCollapse?: boolean;
    initialTreeDepth?: number;
    [key: string]: any;
  }

  // Extend ECOption to include custom series types
  type ECOption = echarts.ComposeOption<
    | BarSeriesOption
    | LineSeriesOption
    | PieSeriesOption
    | ScatterSeriesOption
    | RadarSeriesOption
    | GaugeSeriesOption
    | LinesSeriesOption
    | GraphSeriesOption
    | TreeSeriesOption
  >;
}

// Extend LineStyleOption to include curveness and emphasis
declare module "echarts/types/src/util/types" {
  interface LineStyleOption {
    curveness?: number;
    emphasis?: any;
  }
}

// Extend label position options
declare module "echarts/types/src/component/tooltip" {
  interface LabelOption {
    position?:
      | "top"
      | "left"
      | "right"
      | "bottom"
      | "inside"
      | "insideLeft"
      | "insideRight"
      | "insideTop"
      | "insideBottom"
      | "insideTopLeft"
      | "insideTopRight"
      | "insideBottomLeft"
      | "insideBottomRight"
      | [number, number]
      | "start"
      | "middle"
      | "end"
      | "insideStart"
      | "insideStartTop"
      | "insideStartBottom"
      | "insideMiddle"
      | "insideMiddleTop"
      | "insideMiddleBottom"
      | "insideEnd"
      | "insideEndTop"
      | "insideEndBottom"
      | "bottom";
  }
}

// Extend XAxis type to include data property
declare module "echarts/types/src/coord/cartesian/AxisModel" {
  interface CartesianAxisOption {
    data?: any[];
  }
}
