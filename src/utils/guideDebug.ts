/**
 * 用户指南系统调试工具
 * 用于诊断和调试指南系统的问题
 */

export interface DebugInfo {
  elementsFound: Record<string, boolean>;
  elementsDetails: Record<string, any>;
  pageInfo: {
    url: string;
    title: string;
    route: string;
  };
  guideConfig: any;
}

/**
 * 检查指南相关的DOM元素
 */
export function checkGuideElements(): Record<string, boolean> {
  const selectors = [
    // 导航相关
    "#collapseIcon",
    "#breadcrumb", 
    ".tree-filter",
    
    // 标签页相关
    ".tabs-header",
    ".topology-tab-content",
    ".project-tab-content",
    
    // 拓扑相关
    "#topologyControls",
    ".topology-controls",
    ".floating-buttons",
    
    // 设备相关
    ".table-box",
    ".device-list",
    
    // 头部功能
    "#language",
    "#darkMode", 
    "#themeSetting",
    "#fullscreen",
    "#guide",
    "#message",
    "#searchMenu"
  ];
  
  const results: Record<string, boolean> = {};
  
  selectors.forEach(selector => {
    const element = document.querySelector(selector);
    results[selector] = !!element;
    
    if (element) {
      console.log(`✅ Found element: ${selector}`, element);
    } else {
      console.log(`❌ Missing element: ${selector}`);
    }
  });
  
  return results;
}

/**
 * 获取元素详细信息
 */
export function getElementDetails(selector: string) {
  const element = document.querySelector(selector);
  
  if (!element) {
    return null;
  }
  
  return {
    tagName: element.tagName,
    id: element.id,
    className: element.className,
    visible: isElementVisible(element),
    position: element.getBoundingClientRect(),
    innerHTML: element.innerHTML.substring(0, 100) + "..."
  };
}

/**
 * 检查元素是否可见
 */
export function isElementVisible(element: Element): boolean {
  const rect = element.getBoundingClientRect();
  const style = window.getComputedStyle(element);
  
  return (
    rect.width > 0 &&
    rect.height > 0 &&
    style.display !== "none" &&
    style.visibility !== "hidden" &&
    style.opacity !== "0"
  );
}

/**
 * 生成调试报告
 */
export function generateDebugReport(): DebugInfo {
  const elementsFound = checkGuideElements();
  const elementsDetails: Record<string, any> = {};
  
  Object.keys(elementsFound).forEach(selector => {
    if (elementsFound[selector]) {
      elementsDetails[selector] = getElementDetails(selector);
    }
  });
  
  return {
    elementsFound,
    elementsDetails,
    pageInfo: {
      url: window.location.href,
      title: document.title,
      route: window.location.pathname
    },
    guideConfig: {
      driverJsLoaded: typeof window !== "undefined" && "driver" in window,
      userAgent: navigator.userAgent,
      screenSize: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }
  };
}

/**
 * 打印调试信息到控制台
 */
export function printDebugInfo() {
  console.group("🔍 用户指南系统调试信息");
  
  const report = generateDebugReport();
  
  console.group("📍 页面信息");
  console.log("URL:", report.pageInfo.url);
  console.log("标题:", report.pageInfo.title);
  console.log("路由:", report.pageInfo.route);
  console.groupEnd();
  
  console.group("🎯 元素检测结果");
  const foundCount = Object.values(report.elementsFound).filter(Boolean).length;
  const totalCount = Object.keys(report.elementsFound).length;
  console.log(`找到 ${foundCount}/${totalCount} 个元素`);
  
  Object.entries(report.elementsFound).forEach(([selector, found]) => {
    if (found) {
      console.log(`✅ ${selector}`);
    } else {
      console.log(`❌ ${selector}`);
    }
  });
  console.groupEnd();
  
  console.group("📋 元素详情");
  Object.entries(report.elementsDetails).forEach(([selector, details]) => {
    console.log(`${selector}:`, details);
  });
  console.groupEnd();
  
  console.group("⚙️ 系统配置");
  console.log("Driver.js 已加载:", report.guideConfig.driverJsLoaded);
  console.log("屏幕尺寸:", report.guideConfig.screenSize);
  console.log("用户代理:", report.guideConfig.userAgent);
  console.groupEnd();
  
  console.groupEnd();
  
  return report;
}

/**
 * 模拟指南步骤
 */
export function simulateGuideSteps(selectors: string[]) {
  console.group("🎮 模拟指南步骤");
  
  selectors.forEach((selector, index) => {
    const element = document.querySelector(selector);
    
    if (element) {
      console.log(`步骤 ${index + 1}: ✅ ${selector}`);
      
      // 高亮元素（添加临时样式）
      const originalStyle = element.getAttribute("style") || "";
      element.setAttribute("style", originalStyle + "; outline: 3px solid #409eff; outline-offset: 2px;");
      
      // 2秒后移除高亮
      setTimeout(() => {
        element.setAttribute("style", originalStyle);
      }, 2000);
      
    } else {
      console.log(`步骤 ${index + 1}: ❌ ${selector} (元素未找到)`);
    }
  });
  
  console.groupEnd();
}

/**
 * 检查指南系统依赖
 */
export function checkDependencies() {
  console.group("📦 依赖检查");
  
  const checks = [
    {
      name: "driver.js",
      check: () => typeof window !== "undefined" && "driver" in window,
      required: true
    },
    {
      name: "Vue 3",
      check: () => typeof window !== "undefined" && window.Vue && window.Vue.version?.startsWith("3"),
      required: true
    },
    {
      name: "Element Plus",
      check: () => typeof window !== "undefined" && "ElMessage" in window,
      required: false
    }
  ];
  
  checks.forEach(({ name, check, required }) => {
    const passed = check();
    const status = passed ? "✅" : (required ? "❌" : "⚠️");
    console.log(`${status} ${name}: ${passed ? "已加载" : "未加载"}`);
  });
  
  console.groupEnd();
}

/**
 * 在开发环境下自动启用调试
 */
if (process.env.NODE_ENV === "development") {
  // 将调试函数添加到全局对象，方便在控制台调用
  if (typeof window !== "undefined") {
    (window as any).guideDebug = {
      checkElements: checkGuideElements,
      getElementDetails,
      generateReport: generateDebugReport,
      printInfo: printDebugInfo,
      simulate: simulateGuideSteps,
      checkDeps: checkDependencies
    };
    
    console.log("🔧 指南调试工具已加载，使用 window.guideDebug 访问调试功能");
  }
}
