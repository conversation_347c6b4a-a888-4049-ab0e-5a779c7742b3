import { reactive } from "vue";
import { FormRules } from "element-plus";
import { useI18n } from "vue-i18n";

// 邮箱正则表达式
export const emailRegex =
  /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[^\s@](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;

// 手机号正则表达式（中国大陆手机号）
export const phoneRegex = /^(?:(?:\+|00)86)?1(?:6[567]|7[013-8]|3\d|5[0-35-9]|8\d|9\d)\d{8}$/;

// 密码正则表达式（至少8位，包含字母和数字）
export const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d$@!%*#?&_.=]{8,}$/;

/**
 * 获取邮箱验证规则
 */
export const getEmailRules = () => {
  const { t } = useI18n();
  return reactive<FormRules>({
    email: [
      { required: true, message: t("user.emailTip"), trigger: "blur" },
      {
        pattern: emailRegex,
        message: t("user.emailErrorTip"),
        trigger: "blur"
      }
    ]
  });
};

/**
 * 获取手机号验证规则
 */
export const getPhoneRules = () => {
  const { t } = useI18n();
  return reactive<FormRules>({
    phone: [
      { required: true, message: t("user.phoneTip"), trigger: "blur" },
      {
        pattern: phoneRegex,
        message: t("user.phoneErrorTip"),
        trigger: "blur"
      }
    ]
  });
};

/**
 * 获取验证码验证规则
 */
export const getCodeRules = () => {
  const { t } = useI18n();
  return reactive<FormRules>({
    code: [
      { required: true, message: t("user.codeTip"), trigger: "blur" },
      { min: 4, max: 6, message: t("user.codeFormatTip"), trigger: "blur" }
    ]
  });
};

/**
 * 获取密码验证规则
 */
export const getPasswordRules = () => {
  const { t } = useI18n();
  return reactive<FormRules>({
    password: [
      { required: true, message: t("device.passwordPlaceholder"), trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (!passwordRegex.test(value)) {
            callback(new Error(t("user.passwordRuleTip")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  });
};

/**
 * 获取邮箱或手机号验证规则
 */
export const getEmailOrPhoneRules = () => {
  const { t } = useI18n();
  return reactive<FormRules>({
    emailOrPhone: [
      { required: true, message: t("user.emailOrPhoneTip"), trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (!emailRegex.test(value) && !phoneRegex.test(value)) {
            callback(new Error(t("user.emailOrPhoneErrorTip")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  });
};
