<template>
  <div class="mac-search-container">
    <div class="search-card">
      <div class="card-header">
        <h3>{{ $t("common.macSearch") }}</h3>
        <p class="description">{{ $t("common.macSearchDescription") }}</p>
      </div>

      <div class="mac-input-container">
        <label class="input-label">{{ $t("common.macAddress") }}</label>
        <div class="mac-input">
          <div v-for="(part, index) in macParts" :key="index" class="mac-part">
            <el-input
              ref="macInputRefs"
              v-model="macParts[index]"
              type="text"
              :placeholder="'00-FF'"
              @input="handleInput(index)"
              @keydown="handleKeydown($event as KeyboardEvent, index)"
              maxlength="2"
              :disabled="disabled"
              class="mac-part-input"
            />
            <span v-if="index !== 5" class="split">:</span>
          </div>
        </div>
      </div>

      <div class="button-container">
        <el-button type="primary" :icon="Search" @click="searchMac" class="action-button search-button">
          {{ $t("common.search") }}
        </el-button>
        <el-button type="danger" :icon="Delete" @click="clearMac" class="action-button clear-button">
          {{ $t("common.clear") }}
        </el-button>
      </div>
    </div>

    <div class="result-card" v-if="organization || isLoading">
      <div class="card-header">
        <h3>{{ $t("common.searchResults") }}</h3>
        <el-tag type="info" v-if="isLoading">{{ $t("common.searching") }}</el-tag>
      </div>

      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="2" animated />
      </div>

      <div v-else class="result-content">
        <div class="result-item">
          <div class="result-label">{{ $t("common.macOrganization") }}</div>
          <div class="result-value">{{ organization || $t("common.noData") }}</div>
        </div>

        <div class="result-item">
          <div class="result-label">{{ $t("common.macOrganizationAddress") }}</div>
          <div class="result-value">{{ organizationAddress || $t("common.noData") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MacSearchComponents">
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { Search, Delete } from "@element-plus/icons-vue";
import { getMacInfo } from "@/api/modules/tools";
import { ElMessage } from "element-plus";
import { Tools } from "@/api/interface";

const { t } = useI18n();

// 响应式状态
const macParts = ref(["", "", "", "", "", ""]);
const macInputRefs = ref<HTMLElement[]>([]);
const organization = ref("");
const organizationAddress = ref("");
const disabled = ref(false);
const isLoading = ref(false);

// 方法
const handleInput = (index: number) => {
  let value = macParts.value[index];
  value = value.replace(/[^0-9A-Fa-f]/g, "").toUpperCase();
  macParts.value[index] = value;

  if (value.length === 2 && index < 5) {
    macInputRefs.value[index + 1]?.focus();
  }
};

const handleKeydown = (event: KeyboardEvent, index: number) => {
  if (event.key === "Backspace" && macParts.value[index].length === 0 && index > 0) {
    macInputRefs.value[index - 1]?.focus();
  }
};

const searchMac = async () => {
  const mac = macParts.value.join("-").toUpperCase();
  if (!mac || macParts.value.some(part => part.length !== 2)) {
    ElMessage.warning(t("common.macTip"));
    return;
  }

  isLoading.value = true;
  organization.value = "";
  organizationAddress.value = "";

  try {
    const response = await getMacInfo(mac);
    if (response.code === "200" && response.data) {
      // 使用类型断言确保 TypeScript 识别正确的类型
      const macInfo = response.data as Tools.MacInfoResponse;
      organization.value = macInfo.organization;
      organizationAddress.value = macInfo.organizationAddress;
    } else {
      ElMessage.warning(t("common.noDataTip"));
      organization.value = "";
      organizationAddress.value = "";
    }
  } catch (error) {
    console.error(t("common.operationFail"), error);
    ElMessage.error(t("common.operationFail"));
    organization.value = "";
    organizationAddress.value = "";
  } finally {
    isLoading.value = false;
  }
};

const clearMac = () => {
  macParts.value = ["", "", "", "", "", ""];
  macInputRefs.value[0]?.focus();
  organization.value = "";
  organizationAddress.value = "";
};

onMounted(() => {
  macInputRefs.value = Array.from(document.querySelectorAll(".mac-input .el-input__inner")) as HTMLElement[];
});
</script>

<style scoped>
.mac-search-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-card,
.result-card {
  background-color: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s ease;
}

.search-card:hover,
.result-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: 0;
}

.mac-input-container {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.mac-input {
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mac-part {
  position: relative;
  margin: 0 2px;
  display: flex;
  align-items: center;
}

.mac-part-input {
  width: 50px;
  transition: all 0.3s ease;
}

.mac-part-input :deep(.el-input__inner) {
  text-align: center;
  font-family: monospace;
  font-weight: 500;
  letter-spacing: 1px;
}

.split {
  margin: 0 2px;
  font-weight: 600;
  color: var(--el-text-color-secondary);
}

.button-container {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-button {
  flex: 2;
}

.clear-button {
  flex: 1;
}

.result-content {
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 16px;
}

.result-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.result-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.result-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.result-value {
  font-size: 16px;
  color: var(--el-text-color-primary);
  word-break: break-word;
}

.loading-container {
  padding: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mac-input {
    flex-wrap: wrap;
    justify-content: center;
  }

  .mac-part {
    margin: 5px;
  }
}
</style>
