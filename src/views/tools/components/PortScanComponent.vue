<template>
  <div class="port-scan">
    <div class="scan-card">
      <div class="card-header">
        <h3>{{ $t("common.portScan") }}</h3>
        <p class="description">{{ $t("common.portScanDescription") }}</p>
      </div>

      <el-form ref="formRef" :model="formData" :rules="rules" class="scan-form">
        <div class="form-row">
          <div class="input-group">
            <label class="input-label">{{ $t("common.ipAddress") }}</label>
            <el-form-item prop="ip" class="form-item">
              <el-input
                v-model="formData.ip"
                :placeholder="$t('device.ipPlaceholder')"
                class="ip-input"
                clearable
                @input="validateIpInput"
                :status="ipInputStatus"
              >
                <template #prefix>
                  <el-icon><Monitor /></el-icon>
                </template>
              </el-input>
              <div v-if="ipInputStatus === 'error'" class="error-message">
                {{ ipErrorMessage }}
              </div>
            </el-form-item>
          </div>

          <div class="port-range">
            <label class="input-label">{{ $t("common.portRange") }}</label>
            <div class="port-inputs">
              <el-form-item prop="portStart" class="form-item">
                <el-input-number
                  v-model="formData.portStart"
                  :placeholder="$t('common.startingPort')"
                  class="port-input"
                  :min="1"
                  :max="65535"
                  :controls="false"
                />
              </el-form-item>
              <span class="port-separator">-</span>
              <el-form-item prop="portEnd" class="form-item">
                <el-input-number
                  v-model="formData.portEnd"
                  :placeholder="$t('common.endingPort')"
                  class="port-input"
                  :min="1"
                  :max="65535"
                  :controls="false"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <div class="button-group">
          <el-button type="primary" @click="startScan" :loading="isLoading" class="action-button">
            <el-icon><Search /></el-icon>
            {{ $t("common.startingScan") }}
          </el-button>
          <el-button type="danger" @click="resetForm" class="action-button">
            <el-icon><Delete /></el-icon>
            {{ $t("common.reset") }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 扫描结果卡片 -->
    <div class="result-card" v-if="scanResults.length > 0 || isLoading">
      <div class="card-header">
        <h3>{{ $t("common.scanResults") }}</h3>
        <el-tag type="success" v-if="scanResults.length > 0">{{ scanResults.length }} {{ $t("common.portsFound") }}</el-tag>
        <el-tag type="info" v-else-if="isLoading">{{ $t("common.scanning") }}</el-tag>
      </div>

      <div class="result-content" v-if="scanResults.length > 0">
        <div class="port-chips">
          <el-tag v-for="result in scanResults" :key="result.port" class="port-chip" type="success" effect="light">
            {{ result.port }}
          </el-tag>
        </div>
      </div>

      <div class="loading-container" v-else-if="isLoading">
        <div class="scanning-animation">
          <div class="radar">
            <div class="radar-beam"></div>
            <div class="ping-circles">
              <div class="ping-circle" v-for="i in 5" :key="i" :style="{ animationDelay: `${i * 0.5}s` }"></div>
            </div>
            <div class="radar-center"></div>
          </div>
          <div class="port-numbers">
            <div
              class="port-number"
              v-for="i in 8"
              :key="i"
              :style="{
                animationDelay: `${i * 0.3}s`,
                left: `${10 + Math.random() * 80}%`,
                top: `${10 + Math.random() * 80}%`
              }"
            >
              {{ Math.floor(formData.portStart + (formData.portEnd - formData.portStart) * Math.random()) }}
            </div>
          </div>
        </div>
        <p class="scanning-text">
          {{ $t("common.scanningPorts") }}<span class="dots"><span>.</span><span>.</span><span>.</span></span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { portScan } from "@/api/modules/tools";
import { useI18n } from "vue-i18n";
import type { FormInstance } from "element-plus";
import { Delete, Search, Monitor } from "@element-plus/icons-vue";

// 组件的状态和数据
const formData = ref({
  ip: "",
  portStart: 8000,
  portEnd: 8500
});
const scanResults = ref<{ port: number; status: string }[]>([]);
const isLoading = ref(false);
const ipInputStatus = ref("");
const ipErrorMessage = ref("");

const { t } = useI18n();

// 验证IP地址格式
const isValidIP = (ip: string): boolean => {
  const ipRegex =
    /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
};

// 验证IP输入
const validateIpInput = () => {
  if (!formData.value.ip) {
    ipInputStatus.value = "";
    ipErrorMessage.value = "";
    return;
  }

  // 过滤非法字符，只保留数字、点和删除键
  formData.value.ip = formData.value.ip.replace(/[^0-9.]/g, "");

  // 验证格式
  if (isValidIP(formData.value.ip)) {
    ipInputStatus.value = "";
    ipErrorMessage.value = "";
  } else {
    ipInputStatus.value = "error";
    ipErrorMessage.value = t("common.invalidIPFormat");
  }
};

const rules = {
  ip: [
    {
      required: true,
      message: t("device.ipPlaceholder"),
      trigger: "blur"
    },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value && !isValidIP(value)) {
          callback(new Error(t("common.invalidIPFormat")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  portStart: [
    {
      required: true,
      message: t("common.startingPortTip"),
      trigger: "blur"
    },
    {
      validator: (rule: any, value: number, callback: (error?: Error) => void) => {
        if (value < 1) {
          callback(new Error(t("common.startingPortMin")));
        } else if (value > 65535) {
          callback(new Error(t("common.startingPortMax")));
        } else {
          callback();
        }
      }
    }
  ],
  portEnd: [
    {
      required: true,
      message: t("common.endingPortTip"),
      trigger: "blur"
    },
    {
      validator: (rule: any, value: number, callback: (error?: Error) => void) => {
        if (value < formData.value.portStart) {
          callback(new Error(t("common.endingPortError")));
        } else if (value < 1) {
          callback(new Error(t("common.endingPortMin")));
        } else if (value > 65535) {
          callback(new Error(t("common.endingPortMax")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

const formRef = ref<FormInstance>();

// 端口扫描：负责解析流式数据并更新 UI
const startScan = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
  } catch (error) {
    return;
  }

  scanResults.value = []; // 清空之前的扫描结果
  isLoading.value = true;

  // 添加错误标志
  let hasError = false;

  try {
    // 调用 portScan，获得 ReadableStream 的 reader 对象
    const reader = await portScan(formData.value.ip, formData.value.portStart, formData.value.portEnd);
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    // 循环读取流数据
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      // 将读取到的二进制数据转换为文本，并追加到 buffer 中
      buffer += decoder.decode(value, { stream: true });

      // SSE 事件通常以双换行（"\n\n"）分隔
      const events = buffer.split("\n\n");
      // 保留最后可能不完整的部分
      buffer = events.pop() || "";

      // 逐条处理事件
      for (const eventStr of events) {
        const trimmed = eventStr.trim();
        if (!trimmed) continue;
        // 假设每条 SSE 事件格式类似 "data: [8000, 8001, 8090, 8138, 8443]"
        if (trimmed.startsWith("data:")) {
          const dataStr = trimmed.slice(5).trim();
          // 根捯您提供的示例，数据格式为 "data:IP地址格式不合法" 或 "data:[ 8000, 8001, 8090, 8138, 8443 ]"
          // 判断是否是错误消息（不以方括号开头）
          if (!dataStr.trim().startsWith("[")) {
            // 这是一个错误消息，直接显示给用户
            ElMessage.error(dataStr);
            console.log("收到错误消息:", dataStr);
            hasError = true; // 设置错误标志
            break; // 结束扫描
          }

          try {
            // 尝试解析JSON
            const openPorts: number[] = JSON.parse(dataStr);
            console.log("收到端口数据:", openPorts);

            // 更新扫描结果（采用追加的方式）
            scanResults.value = [
              ...scanResults.value,
              ...openPorts.map((port: number) => ({
                port,
                status: "开放"
              }))
            ];
          } catch (e) {
            console.error("解析 SSE 数据失败:", e, "原始数据:", dataStr);
            // 如果解析失败，尝试将数据作为错误消息显示
            ElMessage.error(dataStr || t("common.scanFailed"));
            hasError = true; // 设置错误标志
            break; // 结束扫描
          }
        }
      }
    }

    // 当流读取结束后，如果没有数据且没有错误则给出提示
    if (scanResults.value.length === 0 && !hasError) {
      ElMessage.warning(t("common.noOpenPorts"));
    }
  } catch (error) {
    console.error("端口扫描失败:", error);
    ElMessage.error(t("common.scanFailed"));
    hasError = true; // 设置错误标志
  } finally {
    isLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  scanResults.value = [];
  ipInputStatus.value = "";
  ipErrorMessage.value = "";
};
</script>

<style scoped>
.port-scan {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scan-card,
.result-card {
  background-color: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s ease;
}

.scan-card:hover,
.result-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: 0;
}

.scan-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
}

.input-group {
  flex: 1;
  min-width: 300px;
}

.port-range {
  display: flex;
  flex-direction: column;
  min-width: 300px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.form-item {
  margin-bottom: 0;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.2;
}

.ip-input {
  width: 100%;
  transition: all 0.3s ease;
}

.port-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.port-input {
  width: 120px;
}

.port-separator {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 10px;
}

.action-button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-content {
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.port-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.port-chip {
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 16px;
  transition: all 0.2s ease;
}

.port-chip:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  gap: 16px;
}

/* 扫描动画 */
.scanning-animation {
  position: relative;
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
}

.radar {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(var(--el-color-primary-rgb), 0.05);
  border: 2px solid rgba(var(--el-color-primary-rgb), 0.2);
  overflow: hidden;
}

.radar-beam {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%);
  background: conic-gradient(
    rgba(var(--el-color-primary-rgb), 0.8) 0deg,
    rgba(var(--el-color-primary-rgb), 0.1) 90deg,
    rgba(var(--el-color-primary-rgb), 0) 180deg,
    rgba(var(--el-color-primary-rgb), 0) 270deg
  );
  animation: radarSpin 3s linear infinite;
  transform-origin: center;
}

.radar-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background-color: var(--el-color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px var(--el-color-primary);
}

.ping-circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ping-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background-color: var(--el-color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: pingPulse 3s ease-out infinite;
}

.port-numbers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.port-number {
  position: absolute;
  font-size: 12px;
  font-weight: 500;
  color: var(--el-color-primary);
  background-color: rgba(var(--el-bg-color-rgb), 0.7);
  padding: 2px 6px;
  border-radius: 10px;
  opacity: 0;
  transform: scale(0.5);
  animation: portNumberAppear 2s ease-out infinite;
}

.scanning-text {
  color: var(--el-text-color-secondary);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.dots span {
  opacity: 0;
  animation: dotFade 1.5s infinite;
  animation-fill-mode: forwards;
}

.dots span:nth-child(1) {
  animation-delay: 0s;
}

.dots span:nth-child(2) {
  animation-delay: 0.5s;
}

.dots span:nth-child(3) {
  animation-delay: 1s;
}

@keyframes radarSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pingPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.1);
    opacity: 1;
  }
  70% {
    opacity: 0.2;
  }
  100% {
    transform: translate(-50%, -50%) scale(5);
    opacity: 0;
  }
}

@keyframes portNumberAppear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes dotFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .input-group,
  .port-range {
    width: 100%;
  }

  .scanning-animation {
    width: 150px;
    height: 150px;
  }
}
</style>
