<template>
  <div class="guide-test-page">
    <div class="card content-box">
      <h2>用户指南系统测试页面</h2>
      
      <div class="test-section">
        <h3>指南功能测试</h3>
        <div class="button-group">
          <el-button type="primary" @click="startCommonGuide">
            启动通用指南
          </el-button>
          <el-button type="success" @click="startProjectGuide">
            启动项目指南
          </el-button>
          <el-button type="info" @click="startTopologyGuide">
            启动拓扑指南
          </el-button>
          <el-button type="warning" @click="startAutoGuide">
            启动智能指南
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h3>特征检测结果</h3>
        <div class="features-grid">
          <div v-for="(value, key) in features" :key="key" class="feature-item">
            <el-tag :type="value ? 'success' : 'danger'">
              {{ key }}: {{ value ? '✓' : '✗' }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>指南状态</h3>
        <div class="status-info">
          <p><strong>指南激活状态:</strong> {{ isGuideActive ? '激活' : '未激活' }}</p>
          <p><strong>当前指南类型:</strong> {{ currentGuideType || '无' }}</p>
        </div>
      </div>

      <div class="test-section">
        <h3>测试元素</h3>
        <div class="test-elements">
          <div id="testElement1" class="test-element">
            测试元素 1
          </div>
          <div id="testElement2" class="test-element">
            测试元素 2
          </div>
          <div class="tree-filter test-element">
            模拟树形筛选器
          </div>
          <div class="topology-tab-content test-element">
            模拟拓扑图内容
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>自定义指南测试</h3>
        <el-button type="primary" @click="startCustomGuide">
          启动自定义指南
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GuideTest">
import { ref, computed, onMounted } from "vue";
import { useGuide } from "@/hooks/useGuide";

const {
  isGuideActive,
  currentGuideType,
  startCommonGuide,
  startProjectGuide,
  startTopologyGuide,
  startAutoGuide,
  startCustomGuide: startCustomGuideHook
} = useGuide();

// 特征检测结果
const features = ref({});

// 更新特征检测结果
const updateFeatures = () => {
  features.value = {
    hasTreeFilter: !!document.querySelector(".tree-filter"),
    hasTopologyTab: !!document.querySelector(".topology-tab-content"),
    hasProjectTab: !!document.querySelector(".project-tab-content"),
    hasDeviceTable: !!document.querySelector(".table-box"),
    hasTabsHeader: !!document.querySelector(".tabs-header"),
    hasTopologyControls: !!document.querySelector("#topologyControls"),
    hasSidebar: !!document.querySelector("#collapseIcon"),
    hasBreadcrumb: !!document.querySelector("#breadcrumb"),
    hasLanguageSwitch: !!document.querySelector("#language"),
    hasThemeSettings: !!document.querySelector("#themeSetting"),
    hasDarkMode: !!document.querySelector("#darkMode"),
    hasFullscreen: !!document.querySelector("#fullscreen"),
    hasMessage: !!document.querySelector("#message"),
    hasSearchMenu: !!document.querySelector("#searchMenu")
  };
};

// 启动自定义指南
const startCustomGuide = () => {
  const customSteps = [
    {
      element: "#testElement1",
      popover: {
        title: "测试元素 1",
        description: "这是第一个测试元素，用于演示自定义指南功能",
        side: "bottom"
      }
    },
    {
      element: "#testElement2",
      popover: {
        title: "测试元素 2",
        description: "这是第二个测试元素，展示指南的连续性",
        side: "top"
      }
    },
    {
      element: ".tree-filter",
      popover: {
        title: "模拟树形筛选器",
        description: "这个元素模拟了项目页面的树形筛选器",
        side: "right"
      }
    },
    {
      element: ".topology-tab-content",
      popover: {
        title: "模拟拓扑图内容",
        description: "这个元素模拟了拓扑图的内容区域",
        side: "left"
      }
    }
  ];

  startCustomGuideHook(customSteps, {
    allowClose: true,
    animate: true,
    opacity: 0.75
  });
};

onMounted(() => {
  updateFeatures();
  
  // 监听DOM变化，更新特征检测结果
  const observer = new MutationObserver(() => {
    updateFeatures();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});
</script>

<style scoped lang="scss">
.guide-test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  
  h3 {
    margin-bottom: 15px;
    color: var(--el-text-color-primary);
    border-bottom: 2px solid var(--el-color-primary);
    padding-bottom: 5px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.feature-item {
  padding: 5px;
}

.status-info {
  background: var(--el-bg-color-page);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  
  p {
    margin: 5px 0;
  }
}

.test-elements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.test-element {
  padding: 20px;
  background: var(--el-color-primary-light-9);
  border: 2px solid var(--el-color-primary);
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  color: var(--el-color-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--el-color-primary-light-8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .test-elements {
    grid-template-columns: 1fr;
  }
}
</style>
