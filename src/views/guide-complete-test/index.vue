<template>
  <div class="guide-complete-test">
    <div class="card content-box">
      <h1>🎯 完整指南系统测试</h1>
      
      <!-- 测试控制面板 -->
      <div class="test-panel">
        <h2>测试控制</h2>
        <div class="control-buttons">
          <el-button type="primary" @click="testProjectGuide" size="large">
            测试项目指南
          </el-button>
          <el-button type="success" @click="testElementDetection" size="large">
            检测页面元素
          </el-button>
          <el-button type="warning" @click="showDebugInfo" size="large">
            显示调试信息
          </el-button>
          <el-button type="info" @click="simulateProjectPage" size="large">
            模拟项目页面
          </el-button>
        </div>
      </div>

      <!-- 模拟项目页面结构 -->
      <div class="mock-project-page" v-if="showMockPage">
        <h2>模拟项目页面结构</h2>
        
        <!-- 模拟左侧项目树 -->
        <div class="tree-filter mock-element">
          <h3>项目选择器</h3>
          <p>模拟的项目树形筛选器</p>
          <div class="mock-dropdown">
            <el-button size="small" id="addProjectBtn">添加项目</el-button>
            <el-button size="small" id="deleteProjectBtn">删除项目</el-button>
          </div>
        </div>

        <!-- 模拟标签页 -->
        <div class="tabs-header mock-element">
          <h3>标签页切换</h3>
          <div class="tab-items">
            <span class="tab-item active">网络拓扑</span>
            <span class="tab-item">项目目录</span>
          </div>
        </div>

        <!-- 模拟拓扑图内容 -->
        <div class="topology-tab-content mock-element">
          <h3>拓扑图内容</h3>
          <div class="topology-mockup">
            <div class="node">路由器</div>
            <div class="node">交换机</div>
            <div class="node">AP</div>
          </div>
          <!-- 模拟拓扑控制按钮 -->
          <div id="topologyControls" class="topology-controls">
            <el-button circle size="small">🔄</el-button>
            <el-button circle size="small">⚡</el-button>
            <el-button circle size="small">🔍</el-button>
          </div>
        </div>

        <!-- 模拟项目目录内容 -->
        <div class="project-tab-content mock-element">
          <h3>设备管理</h3>
          
          <!-- 模拟绑定按钮 -->
          <div class="table-header">
            <el-button type="primary" id="bindDeviceBtn" class="bind-device-button">
              绑定设备
            </el-button>
          </div>

          <!-- 模拟设备表格 -->
          <div class="el-table mock-table">
            <div class="table-row">
              <span>设备1 - 192.168.1.1</span>
              <div class="actions">
                <el-button size="small" class="device-action-btn view-btn">查看</el-button>
                <el-button size="small" class="device-action-btn manage-btn">管理</el-button>
                <el-button size="small" class="device-action-btn unbind-btn">解绑</el-button>
              </div>
            </div>
            <div class="table-row">
              <span>设备2 - 192.168.1.2</span>
              <div class="actions">
                <el-button size="small" class="device-action-btn view-btn">查看</el-button>
                <el-button size="small" class="device-action-btn manage-btn">管理</el-button>
                <el-button size="small" class="device-action-btn unbind-btn">解绑</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 模拟浮动指南按钮 -->
        <div class="floating-guide-button mock-element">
          <el-button type="primary" circle @click="testProjectGuide">
            ❓
          </el-button>
        </div>
      </div>

      <!-- 检测结果显示 -->
      <div class="detection-results" v-if="detectionResults">
        <h2>元素检测结果</h2>
        <div class="results-grid">
          <div 
            v-for="(found, selector) in detectionResults" 
            :key="selector"
            class="result-item"
            :class="{ found, missing: !found }"
          >
            <span class="selector">{{ selector }}</span>
            <span class="status">{{ found ? '✅ 找到' : '❌ 缺失' }}</span>
          </div>
        </div>
      </div>

      <!-- 调试信息显示 -->
      <div class="debug-section" v-if="debugData">
        <h2>调试信息</h2>
        <div class="debug-tabs">
          <el-tabs v-model="activeDebugTab">
            <el-tab-pane label="元素检测" name="elements">
              <pre>{{ JSON.stringify(debugData.elementsFound, null, 2) }}</pre>
            </el-tab-pane>
            <el-tab-pane label="页面信息" name="page">
              <pre>{{ JSON.stringify(debugData.pageInfo, null, 2) }}</pre>
            </el-tab-pane>
            <el-tab-pane label="系统配置" name="config">
              <pre>{{ JSON.stringify(debugData.guideConfig, null, 2) }}</pre>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GuideCompleteTest">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useGuide } from "@/hooks/useGuide";
import { checkGuideElements, generateDebugReport } from "@/utils/guideDebug";

const { startProjectGuide } = useGuide();

const showMockPage = ref(false);
const detectionResults = ref(null);
const debugData = ref(null);
const activeDebugTab = ref("elements");

// 测试项目指南
const testProjectGuide = async () => {
  console.log("Testing project guide...");
  
  try {
    await startProjectGuide({
      allowClose: true,
      animate: true,
      opacity: 0.75,
      padding: 10
    });
    
    ElMessage.success("项目指南启动成功！");
  } catch (error) {
    console.error("Error testing project guide:", error);
    ElMessage.error("项目指南启动失败：" + error.message);
  }
};

// 检测页面元素
const testElementDetection = () => {
  console.log("Testing element detection...");
  
  const results = checkGuideElements();
  detectionResults.value = results;
  
  const foundCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  ElMessage.info(`检测完成：找到 ${foundCount}/${totalCount} 个元素`);
};

// 显示调试信息
const showDebugInfo = () => {
  console.log("Generating debug info...");
  
  try {
    debugData.value = generateDebugReport();
    ElMessage.success("调试信息已生成");
  } catch (error) {
    console.error("Error generating debug info:", error);
    ElMessage.error("生成调试信息失败：" + error.message);
  }
};

// 模拟项目页面
const simulateProjectPage = () => {
  showMockPage.value = !showMockPage.value;
  
  if (showMockPage.value) {
    ElMessage.success("已显示模拟项目页面，现在可以测试指南功能");
    
    // 等待DOM更新后自动检测元素
    setTimeout(() => {
      testElementDetection();
    }, 500);
  } else {
    ElMessage.info("已隐藏模拟项目页面");
  }
};
</script>

<style scoped lang="scss">
.guide-complete-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-panel {
  margin-bottom: 30px;
  
  h2 {
    margin-bottom: 15px;
    color: var(--el-color-primary);
  }
  
  .control-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }
}

.mock-project-page {
  margin-bottom: 30px;
  padding: 20px;
  border: 2px dashed var(--el-border-color);
  border-radius: 12px;
  background: var(--el-bg-color-page);
  
  h2 {
    margin-bottom: 20px;
    color: var(--el-color-success);
  }
}

.mock-element {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 10px 0;
    color: var(--el-color-primary);
  }
  
  p {
    margin: 0 0 10px 0;
    color: var(--el-text-color-regular);
  }
}

.mock-dropdown {
  display: flex;
  gap: 10px;
}

.tab-items {
  display: flex;
  gap: 10px;
  
  .tab-item {
    padding: 8px 16px;
    background: var(--el-color-primary-light-9);
    border-radius: 6px;
    cursor: pointer;
    
    &.active {
      background: var(--el-color-primary);
      color: white;
    }
  }
}

.topology-mockup {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  
  .node {
    padding: 8px 12px;
    background: var(--el-color-success);
    color: white;
    border-radius: 6px;
    font-size: 12px;
  }
}

.topology-controls {
  display: flex;
  gap: 8px;
}

.table-header {
  margin-bottom: 15px;
}

.mock-table {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  
  .table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  }
}

.floating-guide-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.detection-results {
  margin-bottom: 30px;
  
  h2 {
    margin-bottom: 15px;
    color: var(--el-color-warning);
  }
  
  .results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 10px;
    
    .result-item {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      border-radius: 6px;
      
      &.found {
        background: var(--el-color-success-light-9);
        border: 1px solid var(--el-color-success-light-5);
      }
      
      &.missing {
        background: var(--el-color-danger-light-9);
        border: 1px solid var(--el-color-danger-light-5);
      }
      
      .selector {
        font-family: monospace;
        font-size: 12px;
      }
      
      .status {
        font-weight: bold;
      }
    }
  }
}

.debug-section {
  h2 {
    margin-bottom: 15px;
    color: var(--el-color-info);
  }
  
  pre {
    background: var(--el-fill-color-light);
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
}
</style>
