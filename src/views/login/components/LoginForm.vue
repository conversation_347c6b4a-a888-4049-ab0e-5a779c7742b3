<template>
  <el-tabs v-model="activeTab" class="login-tabs">
    <el-tab-pane :label="t('user.accountLogin')" name="account">
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="animated-form" size="large">
        <el-form-item class="form-item-animate" prop="username">
          <el-input v-model="loginForm.username" :placeholder="t('user.userNameTip')" class="custom-input username-input">
            <template #prefix>
              <el-icon class="el-input__icon">
                <user />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="form-item-animate" prop="password">
          <el-input
            v-model="loginForm.password"
            :placeholder="t('user.passwordTip')"
            autocomplete="new-password"
            class="custom-input password-input"
            show-password
            type="password"
          >
            <template #prefix>
              <el-icon class="el-input__icon">
                <lock />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="login-btn">
        <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)"> {{ $t("common.reset") }} </el-button>
        <el-button :icon="UserFilled" :loading="loading" round size="large" type="primary" @click="login(loginFormRef)">
          {{ $t("header.login") }}
        </el-button>
      </div>
    </el-tab-pane>
    <el-tab-pane :label="t('user.codeLogin')" name="code">
      <el-form ref="smsLoginFormRef" :model="smsLoginForm" :rules="smsLoginRules" class="animated-form" size="large">
        <el-form-item class="form-item-animate" prop="phone">
          <el-input v-model="smsLoginForm.phone" :placeholder="t('user.emailOrPhoneTip')" class="custom-input username-input">
            <template #prefix>
              <el-icon class="el-input__icon">
                <iphone />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="form-item-animate" prop="code">
          <el-input v-model="smsLoginForm.code" :placeholder="t('user.codeTip')" class="custom-input code-input">
            <template #prefix>
              <el-icon class="el-input__icon">
                <key />
              </el-icon>
            </template>
            <template #append>
              <el-button :disabled="smsLoginCountdown > 0" @click="sendLoginCode">
                {{ smsLoginCountdown > 0 ? t("user.resendCodeTime", { time: smsLoginCountdown }) : t("user.sendCode") }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="login-btn">
        <el-button :icon="CircleClose" round size="large" @click="resetForm(smsLoginFormRef)">
          {{ $t("common.reset") }}
        </el-button>
        <el-button :icon="UserFilled" :loading="loading" round size="large" type="primary" @click="smsLogin(smsLoginFormRef)">
          {{ $t("header.login") }}
        </el-button>
      </div>
    </el-tab-pane>
  </el-tabs>
  <div class="login-links">
    <el-divider></el-divider>
    {{ t("user.noAccount") }}
    <el-link type="primary" @click.prevent="showRegisterDialog()">{{ t("user.registerNow") }}</el-link>
    <el-link type="primary" @click.prevent="showForgetDialog()">{{ t("user.forgetPassword") }}</el-link>
  </div>
  <el-dialog
    v-model="registerDialogVisible"
    :close-on-click-modal="false"
    :title="t('user.register')"
    :width="dialogWidth"
    :top="isMobile ? '5vh' : '15vh'"
    append-to-body
    class="glass-dialog mobile-dialog"
    @close="closeDialog"
  >
    <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="dialog-form" size="large">
      <el-form-item prop="emailOrPhone">
        <el-input v-model="registerForm.emailOrPhone" :placeholder="t('user.emailOrPhoneTip')">
          <template #append>
            <el-button :disabled="registerCountdown > 0" @click="sendCode">
              {{ registerCountdown > 0 ? t("user.resendCodeTime", { time: registerCountdown }) : t("user.sendCode") }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="registerForm.code" :placeholder="t('user.codeTip')"></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="registerForm.password" :placeholder="t('user.passwordTip')" type="password"></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input v-model="registerForm.confirmPassword" :placeholder="t('user.confirmPasswordTip')" type="password"></el-input>
      </el-form-item>
      <el-form-item prop="nickname">
        <el-input v-model="registerForm.nickname" :placeholder="t('user.nicknameTip')"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="registerDialogVisible = false"> {{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handleRegister(registerFormRef)"> {{ $t("common.confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="forgetPasswordDialogVisible"
    :close-on-click-modal="false"
    :title="t('user.forgetPassword')"
    :width="dialogWidth"
    :top="isMobile ? '5vh' : '15vh'"
    append-to-body
    class="glass-dialog mobile-dialog"
    @close="closeDialog"
  >
    <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="dialog-form" size="large">
      <el-form-item prop="emailOrPhone">
        <el-input v-model="registerForm.emailOrPhone" :placeholder="t('user.emailOrPhoneTip')">
          <template #append>
            <el-button :disabled="forgetCountdown > 0" @click="sendForgetCode">
              {{ forgetCountdown > 0 ? t("user.resendCodeTime", { time: forgetCountdown }) : t("user.sendCode") }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="registerForm.code" :placeholder="t('user.codeTip')"></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="registerForm.password" :placeholder="t('user.passwordTip')" type="password"></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input v-model="registerForm.confirmPassword" :placeholder="t('user.confirmPasswordTip')" type="password"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="forgetPasswordDialogVisible = false"> {{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handleForget(registerFormRef)"> {{ $t("common.confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, reactive, ref, watch, computed } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL } from "@/config";
// import { getTimeState } from "@/utils";
import { Login } from "@/api/interface";
// import { ElNotification } from "element-plus";
import {
  getUserInfo,
  isRegisterApi,
  loginApi,
  registerApi,
  resetPasswordApi,
  saveNewPasswordApi,
  sendCodeApi,
  sendSmsCodeApi
} from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { useGlobalStore } from "@/stores/modules/global";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { CircleClose, UserFilled, Iphone, Key } from "@element-plus/icons-vue";
import { ElForm, ElMessage, ElNotification } from "element-plus";
import { getTimeState } from "@/utils";
import { useI18n } from "vue-i18n";
// import md5 from "md5";

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();
const globalStore = useGlobalStore();

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const smsLoginFormRef = ref<FormInstance>();
const registerFormRef = ref<FormInstance>();
const forgetPasswordFormRef = ref<FormInstance>();

// 激活的标签页
const activeTab = ref("account");
// 邮箱正则表达式
const emailRegex = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
// 手机号正则表达式（中国大陆手机号）
const phoneRegex = /^1[3-9]\d{9}$/;
// 创建响应式的表单验证规则
const createLoginRules = () => ({
  username: [{ required: true, message: t("user.userNameTip"), trigger: "blur" }],
  password: [{ required: true, message: t("user.passwordTip"), trigger: "blur" }]
});

// 创建验证码登录的表单验证规则
const createSmsLoginRules = () => {
  return {
    phone: [
      { required: true, message: t("user.emailOrPhoneTip"), trigger: "blur" },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback();
            return;
          }
          if (!emailRegex.test(value) && !phoneRegex.test(value)) {
            callback(new Error(t("user.emailOrPhoneErrorTip")));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    code: [{ required: true, message: t("user.codeTip"), trigger: "blur" }]
  } as any;
};

const loginRules = reactive(createLoginRules());
const smsLoginRules = reactive(createSmsLoginRules());

const loading = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  username: "",
  password: ""
});

const smsLoginForm = reactive<Login.ReqSmsLoginForm>({
  phone: "",
  code: ""
});

// 验证码登录倒计时
const smsLoginCountdown = ref(0);

// 账号密码登录
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      console.log("调用 loginApi 参数:", { ...loginForm, password: loginForm.password });
      const res = await loginApi({ ...loginForm, password: loginForm.password });
      // 检查返回的数据是否符合预期
      console.log("loginApi 返回的数据:", res);
      if (res.msg !== "success" || !res.access_token) {
        ElMessage.error({ message: res.msg });
        return;
      }
      console.log("res.data.access_token:", res.access_token);
      userStore.setToken(res.access_token);

      const userInfo = await getUserInfo();
      const data = userInfo.data;
      data.name = data.username;
      userStore.setUserInfo(data);

      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.setTabs([]);
      keepAliveStore.setKeepAliveName([]);

      // 4.跳转到首页
      router.push(HOME_URL);
      const displayName = data.username || data.email || data.phone;
      ElNotification({
        title: getTimeState(t),
        message: t("header.welcomeLogin") + ": " + displayName,
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

// 验证码登录
const smsLogin = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行验证码登录接口
      const loginParams: Login.ReqSmsLoginForm = {
        phone: smsLoginForm.phone,
        code: smsLoginForm.code
      };

      console.log("调用 registerApi 参数 (验证码登录):", loginParams);
      const res = await registerApi(loginParams);
      // 检查返回的数据是否符合预期
      console.log("registerApi 返回的数据 (验证码登录):", res);
      if (res.msg !== "success" || !res.access_token) {
        ElMessage.error({ message: res.msg });
        return;
      }
      console.log("res.data.access_token:", res.access_token);
      userStore.setToken(res.access_token);

      const userInfo = await getUserInfo();
      const data = userInfo.data;
      data.name = data.username;
      userStore.setUserInfo(data);

      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.setTabs([]);
      keepAliveStore.setKeepAliveName([]);

      // 4.跳转到首页
      router.push(HOME_URL);
      const displayName = data.username || data.email || data.phone;
      ElNotification({
        title: getTimeState(t),
        message: t("header.welcomeLogin") + ": " + displayName,
        type: "success",
        duration: 3000
      });
      // ElNotification({
      //   title: "React 付费版本 🔥🔥🔥",
      //   dangerouslyUseHTMLString: true,
      //   message: "预览地址：<a href='https://pro.spicyboy.cn'>https://pro.spicyboy.cn</a>",
      //   type: "success",
      //   duration: 8000
      // });
    } finally {
      loading.value = false;
    }
  });
};

const registerDialogVisible = ref(false); // 注册对话框
const forgetPasswordDialogVisible = ref(false); // 忘记密码对话框
const registerCountdown = ref(0); // 注册验证码倒计时
const forgetCountdown = ref(0); // 忘记密码验证码倒计时
const countdownTimer = ref<number | null>(null); // 倒计时定时器

// 响应式窗口大小检测
const windowWidth = ref(window.innerWidth);

// 计算属性：是否为移动端
const isMobile = computed(() => windowWidth.value <= 768);

// 计算属性：弹窗宽度
const dialogWidth = computed(() => {
  if (windowWidth.value <= 480) return "95%";
  if (windowWidth.value <= 768) return "90%";
  return "500px";
});

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

const registerForm = reactive({
  emailOrPhone: "",
  password: "",
  confirmPassword: "",
  code: "",
  nickname: "",
  email: "",
  phone: ""
});

// 创建响应式的注册表单验证规则
const createRegisterRules = () => ({
  emailOrPhone: [
    { required: true, message: t("user.emailOrPhoneTip"), trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        // 邮箱正则表达式
        const emailRegex = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
        // 手机号正则表达式（中国大陆手机号）
        const phoneRegex = /^1[3-9]\d{9}$/;

        if (!value) {
          callback(new Error(t("user.emailOrPhoneErrorTip")));
        } else if (!emailRegex.test(value) && !phoneRegex.test(value)) {
          callback(new Error(t("user.emailOrPhoneErrorTip")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  password: [
    { required: true, message: t("user.passwordTip"), trigger: "blur" },
    {
      pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d$@!%*#?&_.=]{8,}$/,
      message: t("user.passwordRuleTip"),
      trigger: "blur"
    }
  ],
  confirmPassword: [
    { required: true, message: t("user.confirmPasswordTip"), trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!value) {
          callback(new Error(t("user.confirmPasswordTip")));
        } else if (value !== registerForm.password) {
          callback(new Error(t("user.passwordNotMatch")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  code: [
    { required: true, message: t("user.codeTip"), trigger: "blur" },
    { pattern: /^\d{4}$/, message: t("user.codeRuleTip"), trigger: "blur" }
  ],
  nickname: [{ max: 50, message: t("user.nicknameRuleTip"), trigger: "blur" }]
});

const registerRules = reactive(createRegisterRules()) as any;

// 开始倒计时函数
const startCountdown = (type: "register" | "forget" | "smsLogin") => {
  // 清除之前的定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }

  // 设置倒计时初始值
  if (type === "register") registerCountdown.value = 60;
  if (type === "forget") forgetCountdown.value = 60;
  if (type === "smsLogin") smsLoginCountdown.value = 60;

  // 创建定时器，每秒减1
  countdownTimer.value = window.setInterval(() => {
    if (type === "register" && registerCountdown.value > 0) {
      registerCountdown.value--;
    } else if (type === "forget" && forgetCountdown.value > 0) {
      forgetCountdown.value--;
    } else if (type === "smsLogin" && smsLoginCountdown.value > 0) {
      smsLoginCountdown.value--;
    } else {
      // 倒计时结束，清除定时器
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
    }
  }, 1000);
};

const sendCode = async () => {
  if (!registerForm.emailOrPhone) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  if (!emailRegex.test(registerForm.emailOrPhone) && !phoneRegex.test(registerForm.emailOrPhone)) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  const isExist = await isRegisterApi(registerForm.emailOrPhone);
  if (isExist && isExist.code !== "200") {
    ElMessage.warning(t("user.userExist"));
    return;
  }

  try {
    let resp;
    if (emailRegex.test(registerForm.emailOrPhone)) {
      resp = await sendCodeApi(registerForm.emailOrPhone);
    }
    if (phoneRegex.test(registerForm.emailOrPhone)) {
      resp = await sendSmsCodeApi(registerForm.emailOrPhone);
    }
    if (resp && resp.code === "200") {
      ElMessage.success(t("user.codeSent") + ", " + t("user.codeValidTime"));
      // 开始倒计时
      startCountdown("register");
    } else {
      ElMessage.error(t("user.sendCodeFail"));
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error(t("user.sendCodeFail"));
  }
};

const sendForgetCode = async () => {
  if (!registerForm.emailOrPhone) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  if (!emailRegex.test(registerForm.emailOrPhone) && !phoneRegex.test(registerForm.emailOrPhone)) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  const isExist = await isRegisterApi(registerForm.emailOrPhone);
  if (!isExist) {
    ElMessage.warning(t("user.userNotExist"));
    return;
  }

  try {
    let resp;
    if (emailRegex.test(registerForm.emailOrPhone)) {
      resp = await sendCodeApi(registerForm.emailOrPhone);
    }
    if (phoneRegex.test(registerForm.emailOrPhone)) {
      resp = await sendSmsCodeApi(registerForm.emailOrPhone);
    }
    if (resp && resp.code === "200") {
      ElMessage.success(t("user.codeSent") + ", " + t("user.codeValidTime"));
      // 开始倒计时
      startCountdown("forget");
    } else {
      ElMessage.error(t("user.sendCodeFail"));
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error(t("user.sendCodeFail"));
  }
};

const showRegisterDialog = () => {
  registerDialogVisible.value = true;
  resetRegisterForm(registerFormRef.value);
};

const showForgetDialog = () => {
  forgetPasswordDialogVisible.value = true;
  resetForm(forgetPasswordFormRef.value);
};

const closeDialog = () => {
  registerDialogVisible.value = false;
  forgetPasswordDialogVisible.value = false;
  resetForm(registerFormRef.value);

  // 重置倒计时
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  registerCountdown.value = 0;
  forgetCountdown.value = 0;
};

const handleRegister = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) {
      return;
    }
    try {
      registerForm.phone = registerForm.emailOrPhone;

      const result = await registerApi(registerForm);
      console.log("API 响应数据:", JSON.stringify(result));
      if (!result || result.code !== "200") {
        ElMessage.error(result.msg);
        return;
      }
      if (result.code === "200") {
        await saveNewPasswordApi({
          password: registerForm.password,
          confirmPassword: registerForm.confirmPassword
        }).then(async res => {
          if (!res || res.code !== "200") {
            ElMessage.error(res.msg);
            return;
          }
          ElMessage.success(t("user.registerSuccess"));
        });
        registerDialogVisible.value = false;
        resetRegisterForm(formEl);
        return;
      }
    } catch (error) {
      console.error("注册失败:", error);
      ElMessage.error(t("user.registerFail"));
    }
  });
};

const handleForget = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) {
      return;
    }
    try {
      registerForm.phone = registerForm.emailOrPhone;
      const isExist = await isRegisterApi(registerForm.emailOrPhone);
      if (!isExist) {
        ElMessage.warning(t("user.userNotExist"));
        return;
      }

      const result = await resetPasswordApi(registerForm);
      console.log("API 响应数据:", JSON.stringify(result));
      if (!result || result.code !== "200") {
        ElMessage.error(result.msg);
        return;
      }
      if (result.code === "200") {
        ElMessage.success(t("common.resetPasswordSuccess"));
        forgetPasswordDialogVisible.value = false;
        resetRegisterForm(formEl);
        return;
      } else {
        ElMessage.error(result.msg);
      }
    } catch (error) {
      console.error("注册失败:", error);
      ElMessage.error(t("user.registerFail"));
    }
  });
};

const resetRegisterForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

// resetForm
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      if (activeTab.value === "account") {
        login(loginFormRef.value);
      } else if (activeTab.value === "code") {
        smsLogin(smsLoginFormRef.value);
      }
    }
  };

  // 添加窗口大小监听器
  window.addEventListener("resize", handleResize);
});

// 发送登录验证码
const sendLoginCode = async () => {
  if (!smsLoginForm.phone) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  if (!emailRegex.test(smsLoginForm.phone) && !phoneRegex.test(smsLoginForm.phone)) {
    ElMessage.warning(t("user.emailOrPhoneErrorTip"));
    return;
  }

  try {
    let resp;
    if (emailRegex.test(smsLoginForm.phone)) {
      // 发送邮箱验证码
      resp = await sendCodeApi(smsLoginForm.phone);
    } else {
      // 发送手机验证码
      resp = await sendSmsCodeApi(smsLoginForm.phone);
    }

    if (resp && resp.code === "200") {
      ElMessage.success(t("user.codeSent") + ", " + t("user.codeValidTime"));
      // 开始倒计时
      startCountdown("smsLogin");
    } else {
      ElMessage.error(t("user.sendCodeFail"));
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error(t("user.sendCodeFail"));
  }
};

// 监听语言变化，更新表单验证规则
watch(
  () => globalStore.language,
  () => {
    // 更新登录表单验证规则
    Object.assign(loginRules, createLoginRules());
    // 更新验证码登录表单验证规则
    Object.assign(smsLoginRules, createSmsLoginRules());
    // 更新注册表单验证规则
    Object.assign(registerRules, createRegisterRules());
  }
);

onBeforeUnmount(() => {
  document.onkeydown = null;
  // 清除倒计时定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  // 移除窗口大小监听器
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
@import "../index";

// 登录选项卡样式
.login-tabs {
  margin-bottom: 15px;
  :deep(.el-tabs__nav) {
    display: flex;
    width: 100%;
  }
  :deep(.el-tabs__item) {
    flex: 1;
    font-size: 16px;
    text-align: center;
    transition: all 0.3s ease;
    &.is-active {
      font-weight: 600;
      transform: translateY(-2px);
    }
  }
}

// 登录按钮样式优化
.login-btn {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 30px;
  .el-button {
    flex: 1;
    min-height: 44px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 5px 15px rgb(0 0 0 / 10%);
      transform: translateY(-2px);
    }
    &:active {
      transform: translateY(0);
    }
  }
}

// 登录链接样式优化
.login-links {
  margin-top: 25px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  text-align: center;
  .el-divider {
    margin: 20px 0 15px;
  }
  .el-link {
    margin: 0 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 添加表单动画效果
.animated-form {
  .form-item-animate {
    opacity: 0;
    transform: translateY(20px);
    animation: slideUp 0.5s ease forwards;
    &:nth-child(1) {
      animation-delay: 0.2s;
    }
    &:nth-child(2) {
      animation-delay: 0.4s;
    }
  }
}
.login-btn,
.login-links {
  opacity: 0;
  animation: fadeIn 0.5s ease 0.6s forwards;
}

// 验证码输入框样式
.code-input {
  :deep(.el-input-group__append) {
    padding: 0;
    overflow: hidden;
    .el-button {
      height: 100%;
      padding: 0 15px;
      margin: 0;
      font-size: 12px;
      border-radius: 0;
    }
  }
}

// 自定义输入框颜色和效果
.custom-input {
  &.username-input .el-input__icon {
    color: #409eff;
  }
  &.password-input .el-input__icon {
    color: #67c23a;
  }
  .el-input__wrapper {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    &:hover {
      transform: scale(1.02) translateY(-2px);
    }
  }
}

// 响应式设计优化
@media screen and (width <= 768px) {
  .login-tabs {
    margin-bottom: 20px;
    :deep(.el-tabs__item) {
      font-size: 15px;
    }
  }
  .login-btn {
    gap: 12px;
    margin-top: 25px;
    .el-button {
      min-height: 42px;
      font-size: 15px;
    }
  }
  .login-links {
    margin-top: 20px;
    font-size: 13px;
    .el-divider {
      margin: 15px 0 12px;
    }
    .el-link {
      display: inline-block;
      margin: 5px 6px;
      font-size: 13px;
    }
  }
  .code-input {
    :deep(.el-input-group__append) {
      .el-button {
        padding: 0 12px;
        font-size: 11px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  .login-tabs {
    margin-bottom: 15px;
    :deep(.el-tabs__item) {
      font-size: 14px;
    }
  }
  .login-btn {
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
    .el-button {
      width: 100%;
      min-height: 44px;
      font-size: 16px;
    }
  }
  .login-links {
    margin-top: 15px;
    line-height: 1.6;
    .el-divider {
      margin: 12px 0 8px;
    }
    .el-link {
      display: block;
      margin: 8px 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }
  .code-input {
    :deep(.el-input-group__append) {
      .el-button {
        padding: 0 8px;
        font-size: 10px;
      }
    }
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

// 确保对话框中的表单元素样式正常
:deep(.glass-dialog .dialog-form) {
  .el-form-item {
    opacity: 1;
    transform: none;
    animation: none;
  }
}

// 弹窗移动端优化
.mobile-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    margin: 0 !important;
    overflow: hidden;
  }
  :deep(.el-dialog__header) {
    flex-shrink: 0;
    padding: 16px 20px 12px;
  }
  :deep(.el-dialog__body) {
    flex: 1;
    max-height: calc(90vh - 120px);
    padding: 20px;
    overflow-y: auto;
  }
  :deep(.el-dialog__footer) {
    flex-shrink: 0;
    padding: 12px 20px 20px;
  }
}
.dialog-footer {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
  .el-button {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }
}

@media screen and (width <= 768px) {
  .mobile-dialog {
    :deep(.el-dialog) {
      position: fixed;
      top: auto !important;
      bottom: 0;
      width: 100% !important;
      max-height: 85vh;
      margin: 0;
      border-radius: 12px 12px 0 0;
      transform: none !important;
    }
    :deep(.el-dialog__header) {
      padding: 20px 20px 15px;
      border-bottom: 1px solid var(--el-border-color-light);
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
      }
    }
    :deep(.el-dialog__body) {
      max-height: calc(85vh - 130px);
      padding: 20px;
    }
    :deep(.el-dialog__footer) {
      padding: 15px 20px 20px;
      border-top: 1px solid var(--el-border-color-light);
    }
    .dialog-form {
      .el-form-item {
        margin-bottom: 18px;
      }
      .el-input__wrapper {
        min-height: 44px;
        border-radius: 10px;
      }
      .el-button {
        min-height: 42px;
        font-size: 14px;
        border-radius: 8px;
      }
    }
    .dialog-footer {
      gap: 16px;
      .el-button {
        flex: 1;
        min-height: 46px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

@media screen and (width <= 480px) {
  .mobile-dialog {
    :deep(.el-dialog) {
      max-height: 90vh;
      border-radius: 16px 16px 0 0;
    }
    :deep(.el-dialog__header) {
      padding: 16px 16px 12px;
      .el-dialog__title {
        font-size: 17px;
      }
      .el-dialog__headerbtn {
        top: 16px;
        right: 16px;
      }
    }
    :deep(.el-dialog__body) {
      max-height: calc(90vh - 120px);
      padding: 16px;
    }
    :deep(.el-dialog__footer) {
      padding: 12px 16px 16px;
    }
    .dialog-form {
      .el-form-item {
        margin-bottom: 16px;
      }
      .el-input__wrapper {
        min-height: 46px;
        font-size: 16px;
        border-radius: 12px;
      }
      .el-input-group__append {
        .el-button {
          min-height: auto;
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }
    .dialog-footer {
      display: flex;
      gap: 12px;
      justify-content: space-between;
      .el-button {
        flex: 1;
        min-height: 48px;
        font-size: 16px;
        border-radius: 12px;
      }
    }
  }
}

// 黑暗模式下的链接样式增强
:deep(html.dark),
html.dark {
  .login-links {
    .el-link {
      font-weight: 600;
      color: #409eff !important;
      text-shadow: 0 0 8px rgb(64 158 255 / 50%);
      transition: all 0.3s ease;
      &:hover {
        color: #79bbff !important;
        text-shadow: 0 0 12px rgb(64 158 255 / 80%);
        transform: translateY(-2px);
      }
    }
  }
}

// 黑暗模式下的文本颜色增强
html.dark {
  .login-btn {
    color: #e5eaf3 !important;
  }
}
</style>
