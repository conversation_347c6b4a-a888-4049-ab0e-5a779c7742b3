/* 卡片样式 */
.card {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: space-between; /* 垂直分布内容 */
  width: 150px;
  min-height: 135px; /* 设置统一高度 */
  padding: 20px;
  margin-bottom: 10px;
  text-align: center;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
}

/* 图片样式 */
.device-img {
  width: 40px;
  height: 40px;
  object-fit: contain; /* 确保图片比例适配 */
  // margin-bottom: 10px;
}

/* 标题样式 */
.title {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

/* 限制文字行数，避免超出 */
p {
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 为每张卡片增加统一间距 */
// .table-box {
//   display: flex;
//   flex-wrap: wrap;
//   gap: 20px; /* 添加上下和左右的间距 */
// }
.main-box {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
  :deep(.tree-filter) {
    flex: 0 0 280px;
    padding: 16px;
    background-color: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  }
  .table-box {
    flex: 1;
    min-width: 0;
  }
  :deep(.el-table) {
    overflow: hidden;
    border-radius: 16px;
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
    td {
      padding: 12px 0;
    }
    .el-table__row {
      transition: all 0.3s ease;
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
    .el-table__expand-icon {
      transition: all 0.3s ease;
      &:hover {
        transform: scale(1.1);
      }
    }
  }
  :deep(.el-pagination) {
    justify-content: flex-end;
    padding: 0 20px;
    margin-top: 20px;
  }
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
  :deep(.el-button) {
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      transform: translateY(-1px);
    }
  }
  :deep(.el-tag) {
    height: 24px;
    padding: 0 8px;
    line-height: 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    &:hover {
      transform: translateY(-1px);
    }
  }
  :deep(.el-table__header-wrapper) {
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
  }
  :deep(.el-table__body-wrapper) {
    td {
      color: var(--el-text-color-regular);
    }
  }
  :deep(.el-empty) {
    padding: 40px 0;
  }

  // 双表格容器样式
  .split-table-container {
    display: flex;
    width: 100%;
    margin: 10px;

    // 主表格容器
    .main-table-wrapper {
      flex: 1;
      overflow: hidden;
      .nested-table {
        width: 100%;
      }
    }

    // 操作列表格容器
    .operation-table-wrapper {
      flex-shrink: 0;
      width: 200px;
      margin-left: -1px; // 调整边框重叠
      .operation-table {
        width: 100%;

        // 确保操作按钮居中
        .operation-buttons {
          display: flex;
          gap: 8px;
          justify-content: center;
          .el-button--link {
            padding: 4px 0;
            margin: 0 !important;
          }
        }
      }
    }
  }

  // 保留原有的自定义表格容器样式供 AP 列表使用
  .custom-table-wrapper {
    position: relative;
    margin: 10px;

    // 悬浮操作按钮容器
    .floating-actions-container {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 100;
      width: 200px;
      height: 100%;
      pointer-events: none; // 允许点击穿透到下面的元素

      // 标题行
      .floating-header {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 200px;
        height: 45px;
        font-weight: bold;
        color: var(--el-text-color-primary);
        pointer-events: none;
        background-color: var(--el-fill-color-light);
        border-bottom: 1px solid var(--el-border-color);
      }

      // 每一行的悬浮操作按钮
      .floating-actions {
        position: absolute;
        right: 0;
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        width: 200px;
        height: 45px;
        pointer-events: auto; // 恢复点击事件
        background-color: var(--el-bg-color);
        box-shadow: -5px 0 10px rgb(0 0 0 / 10%);
        transition: all 0.2s ease;

        // 悬停效果
        &:hover {
          background-color: var(--el-fill-color-light);
          box-shadow: -8px 0 15px rgb(0 0 0 / 15%);
        }
        .el-button--link {
          padding: 4px 0;
          margin: 0 !important;
        }
      }
    }

    // 表格样式
    .nested-table {
      width: 100%;

      // 表格行高度
      .el-table__row {
        height: 45px;
      }
    }
  }
}

// 响应式布局
@media screen and (width <= 768px) {
  .main-box {
    flex-direction: column;
    padding: 10px;
    :deep(.tree-filter) {
      width: 100%;
      margin-bottom: 20px;
    }
  }
}

// 移动端适配
@media screen and (width <= 480px) {
  .main-box {
    flex-direction: column;
    gap: 8px;
    height: auto;
    padding: 4px;
    border-radius: 8px;
    box-shadow: none;
    :deep(.tree-filter) {
      width: 100%;
      min-width: 0;
      padding: 8px 2px;
      margin-right: 0;
      margin-bottom: 10px;
      border-radius: 6px;
      box-shadow: none;
    }
  }
  .table-box {
    padding: 0;
    border-radius: 6px;
    box-shadow: none;
  }
  :deep(.el-table),
  :deep(.el-table__body),
  :deep(.el-table__header) {
    font-size: 12px;
    border-radius: 5px;
  }
  :deep(.el-table__row) td,
  :deep(.el-table th) {
    padding: 6px 0;
  }
  :deep(.el-pagination) {
    padding: 0 4px;
    margin-top: 8px;
  }

  // ProTable移动端优化
  :deep(.el-table) {
    font-size: 12px;
    border-radius: 5px;

    // 移动端取消固定列，让表格可以横向滚动
    .el-table__fixed,
    .el-table__fixed-right {
      display: none !important;
    }

    // 让表格容器可以横向滚动
    .el-table__body-wrapper {
      overflow-x: auto !important;
      -webkit-overflow-scrolling: touch;
    }

    // 设置最小宽度，确保内容不会被压缩
    .el-table__body {
      min-width: 800px;
    }
    th,
    td {
      padding: 6px 8px;
      font-size: 12px;
    }

    // 操作列优化
    .el-table__cell[class*="operation"] {
      min-width: 200px;
      .el-button {
        padding: 4px 8px;
        margin: 2px;
        font-size: 12px;
      }
    }
  }

  // 表格主体容器优化
  :deep(.table-main) {
    padding: 8px;
    border-radius: 8px;
    .table-header {
      margin-bottom: 8px;
      .header-button-lf,
      .header-button-ri {
        gap: 8px;
        margin-bottom: 8px;
        .el-button {
          padding: 6px 12px;
          font-size: 12px;
          border-radius: 6px;
        }
      }
    }
  }

  // 搜索表单移动端优化
  :deep(.table-search) {
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 8px;
    .el-form-item {
      margin-bottom: 8px;
      .el-form-item__label {
        font-size: 13px;
      }
    }
  }
}

@media screen and (width <= 350px) {
  .main-box {
    gap: 4px;
    padding: 2px;
    border-radius: 4px;
  }
}
