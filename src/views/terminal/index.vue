<template>
  <div class="main-box">
    <TreeFilter
      ref="treeFilterRef"
      label="name"
      :title="t('common.projectList')"
      :request-api="getUserGroup"
      :default-value="initParam.groupId"
      @change="changeTreeFilter"
    />
    <div class="table-box">
      <ProTable
        ref="proTable"
        :key="globalStore.language"
        :columns="columns"
        :request-api="getDeviceListByGroupId"
        :init-param="initParam"
        :expand-row-keys="expandedRows"
        :expand-change="handleExpandChange"
        :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="View" @click="openDrawer(t('common.view'), scope.row)">{{
            t("common.view")
          }}</el-button>
          <el-button
            type="primary"
            v-if="JSON.parse(scope.row.supports).system.supports.includes('userList')"
            link
            :icon="Cellphone"
            @click="loadUserList(t('common.manage'), scope.row)"
            >{{ $t("common.onlineTerminal") }}
          </el-button>
          <el-button
            type="primary"
            v-if="JSON.parse(scope.row.supports).network.supports.includes('brClient')"
            link
            :icon="Cellphone"
            @click="openBridgeClientDrawer(t('common.view'), scope.row)"
            >{{ t("device.bridgeClient") }}
          </el-button>
          <el-button
            type="primary"
            v-if="JSON.parse(scope.row.supports).system.supports.includes('apList')"
            link
            :icon="Cellphone"
            @click="loadApList(t('common.view'), scope.row)"
            >{{ $t("common.aPManage") }}
          </el-button>
          <el-button
            type="primary"
            link
            v-if="JSON.parse(scope.row.supports).system.supports.includes('apGroup')"
            :icon="CopyDocument"
            @click="openApGroupDrawer(t('device.configuration'), scope.row)"
          >
            {{ $t("device.apGroupConfig") }}
          </el-button>
        </template>
        <template #expand="scope">
          <!-- 在线用户列表 -->
          <el-table
            v-if="userListVisible && expandedRowData.userList.length > 0"
            :data="expandedRowData.userList"
            border
            style="margin: 10px; width: 100%"
            :parent-row="scope.row"
          >
            <el-table-column :label="t('device.name')" prop="name">
              <template #default="{ row }">{{ row.name || "--" }}</template>
            </el-table-column>
            <el-table-column :label="t('device.onlineStatusTip')" prop="online">
              <!-- 使用 slot 来自定义列内容 -->
              <template #default="{ row }">
                <el-tag :type="row.online === 1 ? 'success' : 'danger'" disable-transitions>
                  {{ formatOnlineStatus(row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('device.ip')" prop="ipaddr" />
            <el-table-column :label="t('device.mac')" prop="mac">
              <template #default="{ row }">{{ row.mac || "--" }}</template>
            </el-table-column>
            <el-table-column :label="t('terminal.upwardCount')" prop="txByte" :formatter="formatBytes" />
            <el-table-column :label="t('terminal.downwardCount')" prop="rxByte" :formatter="formatBytes" />
            <el-table-column :label="t('common.blackList')" prop="blackList">
              <template #default="{ row }">
                <el-tag v-if="row.blackList == 0" type="success" disable-transitions>
                  {{ t("common.no") }}
                </el-tag>
                <el-tag v-else type="danger" disable-transitions>
                  {{ t("common.yes") }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('device.connectionDuration')" prop="time">
              <template #default="{ row }">{{ formatTime(row.time) }}</template>
            </el-table-column>
            <el-table-column :label="t('device.connectionType')" prop="connect">
              <template #default="{ row }">
                <el-text v-if="row.connect == 'radio0'">
                  {{ t("terminal.connectType.radio0") }}
                </el-text>
                <el-text v-if="row.connect == 'radio1'">
                  {{ t("terminal.connectType.radio1") }}
                </el-text>
                <el-text v-if="row.connect == 'guest'">
                  {{ t("terminal.connectType.guest") }}
                </el-text>
                <el-text v-if="row.connect == 'lan'">
                  {{ t("terminal.connectType.lan") }}
                </el-text>
              </template>
            </el-table-column>
            <!-- 添加操作列 -->
            <el-table-column :label="t('device.operate')" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  :icon="View"
                  @click="openTerminalDrawer(t('common.view'), scope, row)"
                  style="margin-left: 0px !important"
                >
                  {{ $t("common.view") }}
                </el-button>
                <el-button
                  type="primary"
                  link
                  :icon="EditPen"
                  @click="openTerminalDrawer(t('device.configuration'), scope, row)"
                  style="margin-left: 0 !important"
                >
                  {{ $t("device.configuration") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- AP列表 -->
          <el-table
            v-if="apListVisible && expandedRowData.apList.length > 0"
            :data="expandedRowData.apList"
            border
            style="margin: 10px; width: 100%"
            :parent-row="scope.row"
          >
            <el-table-column :label="t('common.alias')" prop="alias" />
            <el-table-column :label="t('device.mac')" prop="mac">
              <template #default="{ row }">{{ row.mac || "--" }}</template>
            </el-table-column>
            <el-table-column :label="t('device.onlineStatusTip')" prop="online">
              <!-- 使用 slot 来自定义列内容 -->
              <template #default="{ row }">
                <el-tag :type="row.online === 1 ? 'success' : 'danger'" disable-transitions>
                  {{ formatOnlineStatus(row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('device.model')" prop="model" />
            <el-table-column :label="t('device.ip')" prop="ipaddr" />
            <el-table-column :label="t('common.version')" prop="version" />
            <el-table-column :label="t('common.group')" prop="group_id">
              <template #default="{ row }">
                <el-text>
                  {{ t("terminal.group" + row.group_id) }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column :label="t('device.ledConfiguration')" prop="ledoff">
              <template #default="{ row }">{{ row.ledoff == 0 ? $t("device.on") : $t("device.off") }}</template>
            </el-table-column>
            <!-- 添加操作列 -->
            <el-table-column :label="t('device.operate')" fixed="right">
              <template #default="subScope">
                <el-button
                  type="primary"
                  link
                  :icon="EditPen"
                  @click="openApDrawer(t('device.configuration'), subScope.row, scope.row, false)"
                  style="margin-left: 0px !important"
                >
                  {{ $t("device.configuration") }}
                </el-button>
                <el-button
                  type="primary"
                  link
                  :icon="View"
                  @click="openApDrawer(t('common.view'), subScope.row, scope.row, true)"
                  style="margin-left: 0 !important"
                >
                  {{ $t("common.view") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty
            :description="t('common.noData')"
            v-if="
              (userListVisible && expandedRowData.userList.length === 0) || (apListVisible && expandedRowData.apList.length === 0)
            "
          ></el-empty>
        </template>
      </ProTable>
      <DeviceDrawer v-if="dataReady" ref="drawerRef" />
      <TerminalDrawer v-if="dataReady" ref="drawerTerminalRef" @close="handleDrawerClose" />
      <DeviceConfigDrawer v-if="dataReady" ref="drawerDeviceConfigRef" />
      <BridgeClientDrawer v-if="dataReady" ref="bridgeClientRef" />
      <ApGroupDrawer v-if="dataReady" ref="apGroupRef" />
      <ProjectDrawer ref="drawerProjectRef" @refresh-tree-filter="updateTreeFilter" />
      <ApDrawer ref="drawerApRef" @refresh-tree-filter="updateTreeFilter" @close="handleDrawerClose" />
      <ImportExcel ref="dialogRef" />
    </div>
  </div>
</template>
<script setup lang="ts" name="useTreeFilter">
import { ref, reactive, onMounted, provide, computed } from "vue";
import { User } from "@/api/interface";
import type { Project } from "@/api/interface/project";
import ProTable from "@/components/ProTable/index.vue";
import TreeFilter from "@/components/TreeFilter/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import DeviceDrawer from "@/views/project/components/DeviceDrawer.vue";
import ProjectDrawer from "@/views/project/components/ProjectDrawer.vue";
import DeviceConfigDrawer from "@/views/project/components/DeviceConfigDrawer.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { View, Cellphone, EditPen, CopyDocument } from "@element-plus/icons-vue";
import { editUser } from "@/api/modules/user";
import { getDeviceListByGroupId, getDeviceType, getUserGroup, getDeviceModel } from "@/api/modules/project";
import { useI18n } from "vue-i18n";
import {
  formatBytes,
  formatOnlineStatus,
  formatTime,
  handleExpandChange as apiHandleExpandChange,
  handleLoadList as apiHandleLoadList,
  createTerminalDrawerParams,
  createBridgeClientDrawerParams,
  createApDrawerParams,
  handleDrawerClose as apiHandleDrawerClose
} from "@/api/modules/terminal";
import { ExpandedRowData } from "@/api/interface/terminal";
import TerminalDrawer from "@/views/terminal/components/TerminalDrawer.vue";
import ApDrawer from "@/views/terminal/components/ApDrawer.vue";
import BridgeClientDrawer from "@/views/terminal/components/BridgeClientDrawer.vue";
import { useGlobalStore } from "@/stores/modules/global";
import { ElMessage } from "element-plus";
import ApGroupDrawer from "@/views/terminal/components/ApGroupDrawer.vue";
import { createApGroupDrawerParams } from "@/api/modules/apGroupDrawer";

const { t } = useI18n();

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

// 添加一个响应式的展开行数据存储
const expandedRowData = reactive<ExpandedRowData>({
  userList: [] as any[],
  apList: [] as any[]
});

// 修改handleExpandChange函数
const handleExpandChange = (row: any, expanded: boolean, dataType?: string) => {
  apiHandleExpandChange(row, expanded, expandedRowData, dataType);
};

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ groupId: "" });

// 树形筛选切换
const changeTreeFilter = (val: string) => {
  // ElMessage.success("请注意查看请求参数变化 🤔");
  proTable.value!.pageable.current = 1;
  initParam.groupId = val;
};

// 存储设备型号的数据
const deviceModels = ref<any[]>([]);
const deviceTypes = ref<any[]>([]);

// 添加一个计算属性来处理状态枚举
const statusEnum = computed(() => [
  {
    label: isChinese.value ? "离线" : "Offline",
    value: 1,
    tagType: "danger"
  },
  {
    label: isChinese.value ? "在线" : "Online",
    value: 0,
    tagType: "success"
  }
]);

// 查询设备型号并更新状态
const loadDeviceModels = async () => {
  const response = await getDeviceModel();
  if (response && response.data) {
    deviceModels.value = response.data; // 将设备型号数据存储到响应式变量中
  }
};

// 使用从 API 模块导入的 formatTime 函数

// 表格配置项
const columns = computed<ColumnProps<User.ResUserList>[]>(() => [
  { type: "index", label: "#", width: 80 },
  { type: "expand", label: t("common.expand"), width: 85 },
  { prop: "deviceId", label: t("device.deviceId"), width: 160, search: { el: "input" } },
  {
    prop: "deviceModel",
    label: t("device.model"),
    width: 120,
    sortable: true,
    search: { el: "select", props: { placeholder: t("common.pleaseSelect") } },
    enum: deviceModels,
    fieldNames: { label: "deviceModel", value: "deviceModel" }
  },
  { prop: "deviceName", label: t("device.deviceName") },
  {
    prop: "deviceType",
    label: t("device.type"),
    enum: deviceTypes,
    search: { el: "select", props: { placeholder: t("common.pleaseSelect") } },
    fieldNames: { label: isChinese.value ? "configDesc" : "attribute2", value: "configCode" }
  },
  {
    prop: "bootTime",
    label: t("device.bootTime"),
    render: (scope: { row: any }) => formatTime(scope.row.bootTime)
  },
  { prop: "mac", label: "MAC", width: 180 },
  { prop: "ipaddr", label: t("common.ipAddress"), width: 180 },
  {
    prop: "status",
    label: t("common.deviceStatus"),
    width: 120,
    sortable: true,
    tag: true,
    enum: statusEnum.value,
    search: { el: "select", props: { placeholder: t("common.pleaseSelect") } }
  },
  {
    prop: "creationTime",
    label: t("common.creationTime"),
    width: 180
  },
  { prop: "operation", label: t("device.operate"), width: 330, fixed: "right" }
]);

// 批量添加用户
const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null);

// 打开 drawer(新增、查看、编辑)
const drawerRef = ref<InstanceType<typeof DeviceDrawer> | null>(null);
const drawerProjectRef = ref<InstanceType<typeof ProjectDrawer> | null>(null);
const drawerDeviceConfigRef = ref<InstanceType<typeof DeviceConfigDrawer> | null>(null);
const drawerTerminalRef = ref<InstanceType<typeof TerminalDrawer> | null>(null);
const bridgeClientRef = ref<InstanceType<typeof BridgeClientDrawer> | null>(null);
const apGroupRef = ref<InstanceType<typeof ApGroupDrawer> | null>(null);
const drawerApRef = ref<InstanceType<typeof ApDrawer> | null>(null);
const openDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  const params = {
    title,
    isView: true,
    row: { ...row },
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
};

const openTerminalDrawer = (title: string, scope: any, row: Partial<Project.ReqProjectParams> = {}) => {
  const params = createTerminalDrawerParams(title, scope, row, proTable.value?.getTableList);
  drawerTerminalRef.value?.acceptParams(params);
};

const openBridgeClientDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  const params = createBridgeClientDrawerParams(title, row, editUser, proTable.value?.getTableList);
  bridgeClientRef.value?.acceptParams(params);
};

const openApGroupDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  // Check status with type safety
  if ((row as any).status !== undefined && (row as any).status === 1) {
    // Show error message
    ElMessage.error(t("device.manageOfflineDeviceTip"));
    // Return the same structure to maintain type compatibility
    return;
  }
  const params = createApGroupDrawerParams(title, row, proTable.value?.getTableList);
  apGroupRef.value?.acceptParams(params);
};

const openApDrawer = (
  title: string,
  row: Partial<Project.ReqProjectParams> = {},
  parentRow: Partial<Project.ReqProjectParams> = {},
  isView: boolean = false
) => {
  // Convert ReqProjectParams to DeviceData for ApDrawer
  const params = createApDrawerParams(title, row as any, parentRow as any, isView, proTable.value?.getTableList);
  drawerApRef.value?.acceptParams(params as any);
};

const expandedRows = ref<any[]>([]); // 存储展开的行 ID
const userListVisible = ref(true);
const apListVisible = ref(false);
const lastClickType = ref<string>(""); // 记录上次点击的按钮类型

const handleLoadList = (row: any, type: string) => {
  const result = apiHandleLoadList(
    row,
    type,
    expandedRows.value,
    lastClickType.value,
    userListVisible.value,
    apListVisible.value,
    t
  );

  if (!result.success) {
    ElMessage({
      message: result.message,
      type: "error",
      plain: true
    });
    return;
  }

  expandedRows.value = result.expandedRows;
  userListVisible.value = result.userListVisible;
  apListVisible.value = result.apListVisible;
  lastClickType.value = result.lastClickType;

  if (expandedRows.value.length > 0) {
    handleExpandChange(row, true, type);
  }
};

const loadUserList = (title: string, row: any = {}) => {
  handleLoadList(row, "userList");
};

const loadApList = (title: string, row: any = {}) => {
  handleLoadList(row, "apList");
};

// 引入组件实例
const treeFilterRef = ref();
const updateTreeFilter = () => {
  console.log("updateTreeFilter----");
  treeFilterRef.value.refreshData();
};

// 在 setup 中调用 provide，提供初始值
provide("deviceTypes", deviceTypes);
const dataReady = ref(false);

// 在组件挂载时加载数据
onMounted(async () => {
  // 加载设备列表数据
  // await fetchDeviceList();
  await loadDeviceModels();
  const response = await getDeviceType();
  if (response && response.data) {
    deviceTypes.value = Array.isArray(response.data) ? response.data : [];
    provide("deviceTypes", deviceTypes.value);
    // console.log("存入deviceTypes:", deviceTypes.value);
    dataReady.value = true; // 数据加载完成后渲染子组件
  }
});

// 修改handleTerminalDrawerClose函数
const handleDrawerClose = async () => {
  const result = await apiHandleDrawerClose(
    expandedRows.value,
    expandedRowData,
    userListVisible.value,
    apListVisible.value,
    () => proTable.value?.tableData
  );

  if (result.userListVisible !== undefined) {
    userListVisible.value = result.userListVisible;
  }

  if (result.apListVisible !== undefined) {
    apListVisible.value = result.apListVisible;
  }

  if (result.expandedRows !== undefined) {
    expandedRows.value = result.expandedRows;
  }
};
</script>
<style scoped lang="scss">
@import "./index";
</style>
