<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="isMobile ? '95vw' : '550px'"
    :title="`${drawerProps.title} ${t('device.bridgeClient')}`"
    @open="handleOpen"
    @close="handleClose"
  >
    <div v-for="(child, index) in data.children" :key="index">
      <span>{{ child.name }}</span>
      <img :src="child.symbol" alt="" />
    </div>
    <el-dialog v-model="bridgeClientDrawerVisible" :title="$t('device.bridgeClient')" width="600px" draggable>
      <!--      {{ clickNodeProps.row }}-->
      <el-form
        ref="ruleFormRef"
        label-width="180px"
        label-suffix=" :"
        :rules="rules"
        :model="clickNodeProps.row"
        :hide-required-asterisk="clickNodeProps.isView"
      >
        <el-form-item :label="$t('device.name')" prop="name">
          <el-text v-if="!editName">{{ clickNodeProps.row?.name }}</el-text>
          <el-input
            v-model="clickNodeProps.row.name"
            v-if="editName"
            clearable
            style="width: 150px"
            @input="deviceNameChanged = true"
          ></el-input>
          <el-link target="_blank" :icon="Edit" @click="editDeviceName(clickNodeProps.row)" v-if="!editName">
            {{ $t("common.edit") }}
          </el-link>
          <el-link
            type="primary"
            target="_blank"
            :icon="Check"
            @click="saveDeviceName(clickNodeProps.row, t)"
            v-if="editName"
            :disabled="deviceNameChanged === false"
          >
            {{ $t("common.save") }}
          </el-link>
        </el-form-item>
        <el-form-item :label="$t('device.deviceType')" prop="type">
          <span v-text="clickNodeProps.row?.extra?.type === 1 ? t('device.bridgeClient') : t('terminal.origin.unknow')"></span>
        </el-form-item>
        <el-form-item :label="$t('device.model')" prop="model">
          <el-text>{{ clickNodeProps.row?.extra?.model ? clickNodeProps.row.extra.model : "--" }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.status')" prop="online" v-if="clickNodeProps.isView">
          <el-text :style="{ color: statusTagType === 'danger' ? 'red' : 'green' }">
            {{ statusLabel }}
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('common.version')" prop="deviceModel">
          <el-text>{{ clickNodeProps.row?.extra?.version ? clickNodeProps.row.extra.version : "--" }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.mac')">
          <el-text>{{ clickNodeProps.row?.extra?.macaddr }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('terminal.upwardCount')" prop="blackList">
          <el-text>
            {{ clickNodeProps.row?.extra?.txByte ? (clickNodeProps.row.extra.txByte / (1024 * 1024)).toFixed(2) : 0 }}MB
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('terminal.downwardCount')" prop="type">
          <el-text>
            {{ clickNodeProps.row?.extra?.rxByte ? (clickNodeProps.row.extra.rxByte / (1024 * 1024)).toFixed(2) : 0 }}MB
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('common.downNegotiationRate')" prop="internet">
          <el-text>{{ clickNodeProps.row?.extra?.txRate ? clickNodeProps.row.extra.txRate : 0 }} Mbps</el-text>
        </el-form-item>
        <el-form-item :label="$t('common.upNegotiationRate')" prop="mac">
          <el-text>{{ clickNodeProps.row?.extra?.rxRate ? clickNodeProps.row.extra.rxRate : 0 }} Mbps</el-text>
        </el-form-item>
        <el-form-item :label="$t('common.latency')" prop="ip">
          <el-text>{{ clickNodeProps.row?.extra?.latency ? clickNodeProps.row.extra.latency : "--" }} ms</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.connectionDuration')">
          <el-text> {{ clickNodeProps.row?.extra?.time ? formatBootTime(clickNodeProps.row.extra.time) : "--" }} </el-text>
        </el-form-item>
        <el-form-item :label="$t('device.rssiTip')">
          <el-text> {{ clickNodeProps.row?.extra?.rssi ? clickNodeProps.row.extra.rssi : "--" }} dBm</el-text>
        </el-form-item>
        <el-form-item style="text-align: center">
          <el-button
            type="primary"
            @click="deleteBridgeClient(clickNodeProps.row, t)"
            :disabled="clickNodeProps.row?.extra?.online == 1"
            >{{ t("common.delete") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <div class="card content-box">
      <div ref="d3TreeRef" class="d3-tree" style="width: 100%; height: 400px"></div>
    </div>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="BridgeClientDrawer">
import { ref, reactive, computed, onMounted, nextTick, watch, toRaw, onBeforeUnmount } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { deviceStatusEnum, getDeviceConfigJwe } from "@/api/modules/project";
import { Check, Edit } from "@element-plus/icons-vue";
import { useGlobalStore } from "@/stores/modules/global";
import * as d3 from "d3";
import {
  deviceNameChanged,
  editName,
  bridgeClientDrawerVisible,
  editDeviceName,
  formatBootTime,
  saveDeviceName,
  drawerVisible,
  clickNodeProps,
  drawerProps,
  generateData,
  data,
  deleteBridgeClient,
  DrawerProps
} from "@/api/interface/bridgeClientDrawer";
import { useUserStore } from "@/stores/modules/user";
import { useWindowSize } from "@vueuse/core";
const { t } = useI18n();
const globalStore = useGlobalStore();

// 响应式设计
const { width: windowWidth } = useWindowSize();
const isMobile = computed(() => windowWidth.value <= 768);

const rules = reactive({
  deviceId: [{ required: true, message: t("device.namePlaceholder") }],
  deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
  deviceType: [{ required: false, message: t("device.typePlaceholder") }],
  mac: [{ required: false, message: t("device.macPlaceholder") }],
  ip: [{ required: false, message: t("device.ipPlaceholder") }]
});

const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const response = await drawerProps.value.api!(drawerProps.value.row);
      console.log("API 响应数据:", response);
      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg });
        return;
      }
      ElMessage.success({ message: `${drawerProps.value.title}设备成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

const status = computed(() => drawerProps.value.row.extra?.online);

const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value?.find(item => item.value !== status.value);
  return statusItem ? statusItem.label : "";
});

const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value?.find(item => item.value !== status.value);
  return statusItem ? statusItem.tagType : "";
});

const handleClose = () => {
  drawerVisible.value = false;
  editName.value = false;
  deviceNameChanged.value = false;
  resetClickNodeProps();

  const container = d3TreeRef.value;
  if (container) container.innerHTML = "";
};

function resetClickNodeProps() {
  clickNodeProps.value = {
    isView: false,
    title: "",
    row: {}
  };
}

const d3TreeRef = ref<HTMLElement | null>(null);

function renderD3Tree() {
  const container = d3TreeRef.value;
  if (!container) return;
  // 清空旧内容
  container.innerHTML = "";
  const width = container.clientWidth || 600;
  const maxTreeHeight = 400;
  const margin = { top: 20, right: 90, bottom: 90, left: 90 };

  const root = d3.hierarchy(toRaw(data));
  const treeLayout = d3.tree().size([width - margin.left - margin.right, maxTreeHeight - margin.top - margin.bottom]);
  treeLayout(root);

  const nodes = root.descendants();
  const minX = d3.min(nodes, d => d.x) ?? 0;
  const maxX = d3.max(nodes, d => d.x) ?? 0;
  const minY = d3.min(nodes, d => d.y) ?? 0;
  const maxY = d3.max(nodes, d => d.y) ?? 0;
  const nodeRadius = 18;

  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;
  const gTranslateX = (width - contentWidth) / 2 - minX;
  const gTranslateY = (maxTreeHeight - contentHeight) / 2 - minY;

  const svg = d3.select(container).append("svg").attr("width", width).attr("height", maxTreeHeight);

  const g = svg.append("g").attr("transform", `translate(${gTranslateX},${gTranslateY})`);

  const offset = 40;
  // 连线
  g.selectAll(".link")
    .data(root.links())
    .enter()
    .append("path")
    .attr("class", "link")
    .attr("fill", "none")
    .attr("stroke", "#ccc")
    .attr("stroke-width", 2)
    .attr("d", d => {
      // 三折线：父节点底部中点 -> 竖直offset -> 水平到子节点x -> 竖直到子节点顶部中点
      const startX = d.source.x;
      const startY = d.source.y + nodeRadius;
      const midY = startY + offset;
      const endX = d.target.x;
      const endY = d.target.y - nodeRadius;
      return `M${startX},${startY}V${midY}H${endX}V${endY}`;
    });

  // 节点
  const node = g
    .selectAll(".node")
    .data(root.descendants())
    .enter()
    .append("g")
    .attr("class", "node")
    .attr("transform", d => `translate(${d.x},${d.y})`)
    .style("cursor", d => (!d.children || d.children.length === 0 ? "pointer" : "default"))
    .on("click", (event, d) => {
      if (!d.children || d.children.length === 0) {
        clickNodeProps.value.row = d.data;
        bridgeClientDrawerVisible.value = true;
      }
    });

  // 节点圆形
  node
    .append("circle")
    .attr("r", 18)
    .attr("fill", globalStore.isDark ? "#444" : "#fff")
    .attr("stroke", "#409EFF")
    .attr("stroke-width", 2);

  // 节点图片（始终用 symbol 路径）
  node
    .append("image")
    .attr("xlink:href", d => {
      let url = d.data.symbol || "";
      if (url.startsWith("image://")) url = url.replace("image://", "");
      return url;
    })
    .attr("x", -12)
    .attr("y", -12)
    .attr("width", 24)
    .attr("height", 24);

  // 离线状态加遮罩层
  node
    .filter(d => d.data.extra?.online === 0)
    .append("rect")
    .attr("x", -12)
    .attr("y", -12)
    .attr("width", 24)
    .attr("height", 24)
    .attr("fill", "#ccc")
    .attr("fill-opacity", 0.5)
    .attr("rx", 12)
    .attr("ry", 12);

  // 节点名称
  node
    .append("text")
    .attr("dy", 32)
    .attr("x", 0)
    .attr("text-anchor", "middle")
    .attr("font-size", 12)
    .attr("fill", globalStore.isDark ? "#fff" : "#333")
    .text(d => d.data.name);
}

const handleOpen = async () => {
  try {
    // 获取用户和设备信息
    const userId = useUserStore().userInfo.userId;
    const deviceId = drawerProps.value.row.deviceId;

    // 发起并行请求获取数据
    const paramsCmd10 = { cmd: 10, deviceId, userId, data: { network: ["brClient"] } };
    const paramsCmd4 = { cmd: 4, deviceId, userId, data: { network: ["brClient"] } };
    const [responseCmd10, responseCmd4] = await Promise.all([getDeviceConfigJwe(paramsCmd10), getDeviceConfigJwe(paramsCmd4)]);

    // 合并返回的数据
    const dataCmd10 = responseCmd10?.data?.network?.brClient || [];
    const dataCmd4 = responseCmd4?.data?.network?.brClient || [];
    const mergedData = mergeBrClientData(dataCmd10, dataCmd4);

    // 将合并数据更新到行数据
    drawerProps.value.row.brClient = mergedData;

    // 更新图表数据
    await generateData(drawerProps.value.row, t);
    data.children = [...data.children]; // 触发响应式更新

    // 等待 DOM 和数据更新完成后初始化图表
    await nextTick(() => {
      renderD3Tree();
    });
  } catch (error) {
    console.error("Error during handleOpen:", error);
  }
};

// 合并 brClient 数据的方法
function mergeBrClientData(dataCmd10, dataCmd4) {
  const mergedData = dataCmd10.map(item10 => {
    const matchingItem = dataCmd4.find(item4 => item4.macaddr === item10.macaddr);
    return matchingItem ? { ...item10, ...matchingItem } : item10;
  });

  dataCmd4.forEach(item4 => {
    if (!mergedData.find(item => item.macaddr === item4.macaddr)) {
      mergedData.push(item4);
    }
  });

  return mergedData;
}

watch(
  () => data.children,
  () => {
    renderD3Tree();
  }
);

watch(
  () => globalStore.isDark,
  () => {
    renderD3Tree();
  }
);

onMounted(() => {
  renderD3Tree();
});

onBeforeUnmount(() => {
  const container = d3TreeRef.value;
  if (container) container.innerHTML = "";
});

// 接收父组件传过来的参数
const acceptParams = async (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
  await generateData(params.row, t);
};

defineExpose({
  acceptParams
});
</script>

<style scoped>
.card.content-box {
  box-sizing: border-box;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  height: 100%;
  min-height: 0;
  padding: 0;
  margin: 0;
}
.d3-tree {
  box-sizing: border-box;
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  justify-content: center;
  max-height: 100%;
  padding: 0;
  margin: 0;
  overflow: auto;
}
.d3-tree svg {
  box-sizing: border-box;
  display: block;
  width: 100%;
}
</style>
