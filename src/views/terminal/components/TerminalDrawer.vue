<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="isMobile ? '95vw' : '550px'"
    :title="`${drawerProps.title} ${t('device.terminal')}`"
    @close="handleClose"
    class="terminal-drawer"
  >
    <el-form
      ref="ruleFormRef"
      :label-width="isMobile ? '' : '160px'"
      label-suffix=" :"
      :label-position="isMobile ? 'top' : 'left'"
      :rules="rules"
      :model="drawerProps.row"
      :disabled="drawerProps.isView"
      :hide-required-asterisk="drawerProps.isView"
      class="terminal-form"
    >
      <el-tabs type="card" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="t('device.deviceInfo')" name="first">
          <el-space wrap>
            <el-image :src="getImageSrc(drawerProps.row?.Origin)" style="width: 80px; height: 80px" />
          </el-space>

          <el-form-item :label="$t('device.name')" prop="name">
            <el-input v-model="drawerProps.row.name" :placeholder="$t('device.namePlaceholder')" clearable></el-input>
          </el-form-item>
          <el-form-item :label="$t('terminal.manufacturer')" prop="name">
            <span v-text="getBrandName(drawerProps.row?.Origin)"></span>
          </el-form-item>
          <el-form-item :label="$t('device.onlineStatusTip')" prop="status">
            <el-text :style="{ color: statusTagType === 'danger' ? 'red' : 'green' }">
              {{ statusLabel }}
            </el-text>
          </el-form-item>
          <el-form-item :label="$t('device.ip')" prop="deviceModel">
            <el-text>{{ drawerProps.row?.ipaddr }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.mac')">
            <el-text>{{ drawerProps.row?.macaddr }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.connectionDuration')" prop="time">
            <el-text>{{ formattedBootTime }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.type')" prop="type">
            <el-text>{{ getTerminalTypeName(drawerProps.row?.type) }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.networkStatusTip')" prop="internet">
            <el-text>{{ getInternetStatusName(drawerProps.row?.internet) }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.connectionTypeTip')" prop="mac">
            <el-text>{{ getConnectType(drawerProps.row?.connect) }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.ip')" prop="ip">
            <el-text>{{ drawerProps.row?.ipaddr }}</el-text>
          </el-form-item>
          <el-form-item :label="$t('device.rssiTip')">
            <el-text> {{ drawerProps.row?.rssi }} dBm</el-text>
          </el-form-item>
          <el-form-item :label="$t('common.addBlackList')" prop="blackList">
            <el-switch
              v-model="drawerProps.row.blackList"
              :active-value="1"
              :inactive-value="0"
              :active-text="t('common.yes')"
              :inactive-text="t('common.no')"
            />
          </el-form-item>
          <el-form-item v-if="drawerProps.row.online === 0">
            <el-button type="danger" :icon="Delete" @click="deleteTerminal()">{{ t("common.delete") }}</el-button>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane :label="t('common.networkLimit')" name="second">
          <el-form-item :label="t('common.networkLimit')" label-width="160px">
            <el-switch :model-value="networkLimitEnabled" @change="handleNetworkLimitChange" />
          </el-form-item>
          <el-form-item :label="t('terminal.upwardLimit')" v-if="networkLimitEnabled" label-width="160px">
            <el-input v-model="rxLimit" :placeholder="t('terminal.upwardLimitPlaceholder')"></el-input>
          </el-form-item>
          <el-form-item :label="t('terminal.downwardLimit')" v-if="networkLimitEnabled" label-width="160px">
            <el-input v-model="txLimit" :placeholder="t('terminal.downwardLimitPlaceholder')"></el-input>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane :label="t('common.internetControl')" name="third">
          <el-form-item :label="t('common.internetControl')" label-width="170px">
            <el-switch :model-value="internetControlEnabled" @change="internetControlChange" />
          </el-form-item>
          <el-form-item :label="t('terminal.controlMode')" v-if="internetControlEnabled" label-width="160px">
            <el-radio-group v-model="drawerProps.row.mode" @change="handleModeChange">
              <el-radio :value="0">{{ t("terminal.allowAccess") }}</el-radio>
              <el-radio :value="1">{{ t("terminal.denyAccess") }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('terminal.onlineTime')" v-if="internetControlEnabled" label-width="120px">
            <el-select v-model="weekValue" multiple :placeholder="t('common.pleaseSelect')" clearable>
              <el-option :label="t('common.selectAll')" :value="'all'" :selected="weekValue.length === 7" />
              <el-option v-for="item in 7" :key="item" :label="t(`common.week${item % 7}`)" :value="item % 7"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="t('device.beginTime')" v-if="internetControlEnabled" label-width="120px">
            <el-time-picker
              v-model="drawerProps.row.beginTime"
              format="HH:mm"
              value-format="HH:mm"
              :placeholder="t('device.beginTimePlaceholder')"
              @input="handleTimeInput('beginTime')"
            />
          </el-form-item>
          <el-form-item :label="t('device.endTime')" v-if="internetControlEnabled" label-width="120px">
            <el-time-picker
              v-model="drawerProps.row.endTime"
              format="HH:mm"
              value-format="HH:mm"
              :placeholder="t('device.endTimePlaceholder')"
              @input="handleTimeInput('endTime')"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :disabled="!hasConfigChanged" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="TerminalDrawer">
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { Project } from "@/api/interface/project";
import { deviceStatusEnum } from "@/api/modules/project";
import { useGlobalStore } from "@/stores/modules/global";
import { Delete } from "@element-plus/icons-vue";
import { getDiff } from "@/utils/diff";
import { useWindowSize } from "@vueuse/core";
const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

const { t } = useI18n();

// 响应式设计
const { width: windowWidth } = useWindowSize();
const isMobile = computed(() => windowWidth.value <= 768);
const activeName = ref("first");

const handleClick = (tab, event) => {
  console.log(tab, event);
};

const rules = reactive({
  deviceId: [{ required: true, message: t("device.namePlaceholder") }],
  deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
  deviceType: [{ required: false, message: t("device.typePlaceholder") }],
  mac: [{ required: false, message: t("device.macPlaceholder") }],
  ip: [{ required: false, message: t("device.ipPlaceholder") }]
});

// Extended interface to include additional properties needed for terminal devices
interface TerminalDeviceData extends Partial<Project.ResDeviceList> {
  rxLimit?: number | string;
  txLimit?: number | string;
  week?: string;
  beginTime?: string | Date;
  endTime?: string | Date;
  mode?: number | string;
  time?: number;
  macaddr?: string;
  blackList?: number;
  name?: string;
  online?: number;
  internet?: number;
  connect?: string;
  rssi?: number | string;
  Origin?: string;
  type?: number;
}

interface DrawerProps {
  title: string;
  isView: boolean;
  row: TerminalDeviceData;
  scope: any;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {},
  scope: {}
});

// 保存原始数据的副本，用于比较是否有变化
const originalData = ref<TerminalDeviceData | null>(null);

// 开关状态
let networkLimitEnabled = ref(false);

let internetControlEnabled = ref(false);

const allWeekValues = [0, 1, 2, 3, 4, 5, 6];
const weekValue = computed({
  get: () => {
    const weekStr = drawerProps.value.row?.week;
    return weekStr
      ? Array.from(
          new Set(
            weekStr
              .split(" ")
              .map(s => Number(s))
              .filter(n => !isNaN(n) && n >= 0 && n <= 6)
          )
        )
      : [];
  },
  set: (value: (number | string)[]) => {
    // 如果包含'all'，全选
    if (value.includes("all")) {
      drawerProps.value.row.week = allWeekValues.join(" ");
      return;
    }
    // 只剩下'all'，清空
    if (value.length === 1 && value[0] === "all") {
      drawerProps.value.row.week = "";
      return;
    }
    // 普通多选
    drawerProps.value.row.week = value.join(" ");
  }
});
// 切换开关时的处理逻辑
const handleNetworkLimitChange = value => {
  networkLimitEnabled.value = value;
  if (!value) {
    // 如果开关关闭，清空 rxLimit 和 txLimit
    drawerProps.value.row.rxLimit = 0;
    drawerProps.value.row.txLimit = 0;
  }
};

// 处理时间输入，转换为字符串
const handleTimeInput = (key: string) => {
  const time = drawerProps.value.row[key];
  if (time instanceof Date) {
    // 将时间转换为 "HH:mm" 格式的字符串
    drawerProps.value.row[key] = time.toTimeString().slice(0, 5);
  }
};

const internetControlChange = value => {
  internetControlEnabled.value = value;
  if (!value) {
    // 关闭时清空所有相关字段
    drawerProps.value.row.week = "";
    drawerProps.value.row.beginTime = "";
    drawerProps.value.row.endTime = "";
    drawerProps.value.row.mode = 0;
  }
};

const handleModeChange = (value: string) => {
  drawerProps.value.row.mode = value;
};

const getImageSrc = (origin: string | undefined): string => {
  if (!origin) {
    // 当 origin 为 undefined、null 或空字符串时，直接返回默认图片
    return new URL("@/assets/images/devices.png", import.meta.url).href;
  }

  const originMap: { [key: string]: string } = {
    xiaomi: new URL("@/assets/images/xiaomi.png", import.meta.url).href,
    huawei: new URL("@/assets/images/huawei.png", import.meta.url).href,
    honor: new URL("@/assets/images/honor.png", import.meta.url).href,
    vivo: new URL("@/assets/images/vivo.png", import.meta.url).href,
    oppo: new URL("@/assets/images/oppo.png", import.meta.url).href,
    zte: new URL("@/assets/images/zte.png", import.meta.url).href,
    samsung: new URL("@/assets/images/samsung.png", import.meta.url).href,
    apple: new URL("@/assets/images/iphone.png", import.meta.url).href,
    unknow: new URL("@/assets/images/devices.png", import.meta.url).href
  };

  return originMap[origin.toLowerCase()] || originMap.unknow;
};

const getBrandName = (origin: string | undefined): string => {
  if (!origin) {
    // 当 origin 为 undefined、null 或空字符串时，直接返回默认名称
    return t("terminal.origin.unknow");
  }

  const brandMap: { [key: string]: string } = {
    xiaomi: t("terminal.origin.xiaomi"),
    huawei: t("terminal.origin.huawei"),
    honor: t("terminal.origin.honor"),
    vivo: t("terminal.origin.vivo"),
    oppo: t("terminal.origin.oppo"),
    zte: t("terminal.origin.zte"),
    samsung: t("terminal.origin.samsung"),
    apple: t("terminal.origin.apple"),
    unknow: t("terminal.origin.unknow")
  };

  return brandMap[origin.toLowerCase()] || brandMap.unknow;
};

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;

  // 保存原始数据的深拷贝，用于后续比较变化
  originalData.value = JSON.parse(JSON.stringify(params.row));
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();

interface ApiResponse {
  code: string;
  msg: string;
  data?: any;
}

const handleSubmit = async () => {
  if (!ruleFormRef.value) return;

  // 上网控制开关打开时，week、beginTime、endTime 必填
  if (internetControlEnabled.value) {
    const week = drawerProps.value.row.week;
    const beginTime = drawerProps.value.row.beginTime;
    const endTime = drawerProps.value.row.endTime;
    const mode = drawerProps.value.row.mode;
    if (!week || week.trim() === "") {
      ElMessage.error(t("terminal.pleaseSelectWeek") || "请选择上网时间");
      return;
    }
    if (mode === undefined || mode === null || mode === "") {
      ElMessage.error(t("terminal.pleaseSelectMode") || "请选择控制模式");
      return;
    }
    if (!beginTime || !endTime) {
      ElMessage.error(t("common.pleaseSelectBeginEndTime"));
      return;
    }
  }

  try {
    let response: ApiResponse;

    // 只取变化的部分
    const diff = getDiff(originalData.value, drawerProps.value.row);
    if (!diff) {
      ElMessage.warning(t("common.noConfigChanges"));
      return;
    }

    // 检查上网控制相关字段
    const hasInternetControlChange = "mode" in diff || "week" in diff || "beginTime" in diff || "endTime" in diff;
    if (hasInternetControlChange) {
      // 校验 week 不为空时，beginTime 和 endTime 必填
      const week = drawerProps.value.row.week;
      const beginTime = drawerProps.value.row.beginTime;
      const endTime = drawerProps.value.row.endTime;
      if (week && (!beginTime || !endTime)) {
        ElMessage.error(t("common.pleaseSelectBeginEndTime"));
        return;
      }
    }

    if (activeName.value === "first") {
      // 确保 scope 和 row 对象存在
      let rowData = { ...diff, macaddr: drawerProps.value.row.macaddr };
      // 如果有上网控制相关字段变化，补全四个字段
      if (hasInternetControlChange) {
        rowData = {
          ...rowData,
          mode: drawerProps.value.row.mode,
          week: drawerProps.value.row.week,
          beginTime: drawerProps.value.row.beginTime,
          endTime: drawerProps.value.row.endTime
        };
      }
      const params = {
        scope: {
          deviceId: drawerProps.value.scope?.deviceId || "",
          row: drawerProps.value.scope || {}
        },
        row: rowData
      };

      if (!drawerProps.value.api) {
        throw new Error(t("common.apiNotDefined"));
      }

      response = await drawerProps.value.api(params);
    } else {
      // 处理其他标签页的提交
      const valid = await ruleFormRef.value.validate();
      if (!valid) return;

      if (!drawerProps.value.api) {
        throw new Error(t("common.apiNotDefined"));
      }

      let rowData = { ...diff, macaddr: drawerProps.value.row.macaddr };
      if (hasInternetControlChange) {
        rowData = {
          ...rowData,
          mode: drawerProps.value.row.mode,
          week: drawerProps.value.row.week,
          beginTime: drawerProps.value.row.beginTime,
          endTime: drawerProps.value.row.endTime
        };
      }
      const params = {
        scope: {
          deviceId: drawerProps.value.scope?.deviceId || "",
          row: drawerProps.value.scope || {}
        },
        row: rowData
      };

      response = await drawerProps.value.api(params);
    }

    if (response.code === "200") {
      ElMessage.success(t("common.operationSuccess"));
      drawerVisible.value = false;
      drawerProps.value.getTableList?.();
    } else {
      ElMessage.error(response.msg || t("common.operationFailed"));
    }
  } catch (error) {
    console.error("保存终端配置时发生错误:", error);
    ElMessage.error(error instanceof Error ? error.message : t("common.operationFailed"));
  }
};

// 格式化 bootTime 方法 - 修改为计算属性
const formattedBootTime = computed(() => {
  const bootTimeInSeconds = drawerProps.value.row?.time || 0;

  const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
  const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
  const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
  const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
  const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
  const seconds = bootTimeInSeconds % 60;

  // 以数组的形式存储各部分
  const timeParts: string[] = [];

  if (years > 0) {
    timeParts.push(`${years} ${isChinese.value ? "年" : "yr"}`);
  }
  if (months > 0) {
    timeParts.push(`${months} ${isChinese.value ? "月" : "mo"}`);
  }
  if (days > 0) {
    timeParts.push(`${days} ${isChinese.value ? "天" : "d"}`);
  }
  if (hours > 0) {
    timeParts.push(`${hours} ${isChinese.value ? "小时" : "h"}`);
  }
  if (minutes > 0) {
    timeParts.push(`${minutes} ${isChinese.value ? "分钟" : "min"}`);
  }
  if (seconds > 0 || timeParts.length === 0) {
    timeParts.push(`${seconds} ${isChinese.value ? "秒" : "s"}`);
  }

  // 拼接时间部分，使用空格分隔
  return timeParts.join(" ");
});

const getInternetStatusName = (status: number) => {
  // 根据 status 获取翻译，默认为 "未知"
  return t(`terminal.internetStatus.${status}`) || t(`terminal.internetStatus.unknown`);
};

const status = computed(() => drawerProps.value.row.online);

const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => item.value !== status.value);
  return statusItem ? statusItem.label : "";
});

const rxLimit = computed({
  get: () => (drawerProps.value.row?.rxLimit === 0 ? "" : drawerProps.value.row?.rxLimit),
  set: value => {
    drawerProps.value.row.rxLimit = value === "" ? 0 : Number(value);
  }
});

const txLimit = computed({
  get: () => (drawerProps.value.row?.txLimit === 0 ? "" : drawerProps.value.row?.txLimit),
  set: value => {
    drawerProps.value.row.txLimit = value === "" ? 0 : Number(value);
  }
});

const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => item.value !== status.value);
  return statusItem ? statusItem.tagType : "";
});

const getTerminalTypeName = (type: number) => {
  console.log("type:", type);
  switch (type) {
    case 1:
      return t("terminal.terminalType.phone"); // 通过 $t 获取国际化文本
    case 2:
      return t("terminal.terminalType.computer").toString(); // 电脑的国际化文本
    case 3:
      return t("terminal.terminalType.camera").toString(); // 摄像头的国际化文本
    default:
      return t("terminal.terminalType.unknown").toString(); // 未知的国际化文本
  }
};

const getConnectType = (connect: string) => {
  switch (connect) {
    case "radio0":
      return t("terminal.connectType.radio0");
    case "radio1":
      return t("terminal.connectType.radio1");
    case "guest":
      return t("terminal.connectType.guest");
    case "lan":
      return t("terminal.connectType.lan");
  }
};

const deleteTerminal = async () => {
  try {
    console.log("deleteTerminal");
    const params = {
      scope: {
        deviceId: drawerProps.value.scope?.deviceId || "",
        row: drawerProps.value.scope || {}
      },
      row: {
        ...drawerProps.value.row,
        delete: 1
      }
    };

    const response = await drawerProps.value.api(params);
    if (response.code === "200") {
      ElMessage.success(t("common.operationSuccess"));
      drawerVisible.value = false;
      drawerProps.value.getTableList?.();
    } else {
      ElMessage.error(response.msg || t("common.operationFailed"));
    }
  } catch (error) {
    console.error("删除终端时发生错误:", error);
    ElMessage.error(error instanceof Error ? error.message : t("common.operationFailed"));
  }
};

// 组件挂载时获取设备类型
onMounted(() => {
  networkLimitEnabled.value = Boolean(drawerProps.value.row.rxLimit || drawerProps.value.row.txLimit);
  // 只在初始化时根据 week 设置上网控制开关
  internetControlEnabled.value = !!(drawerProps.value.row.week && drawerProps.value.row.week.trim() !== "");
});

const handleClose = () => {
  activeName.value = "first";
  originalData.value = null; // 关闭抽屉时清空原始数据
  // 关闭时重置上网控制开关，下次打开自动根据 week 决定
  internetControlEnabled.value = !!(drawerProps.value.row.week && drawerProps.value.row.week.trim() !== "");
};

// 计算属性：检测配置是否有变化
const hasConfigChanged = computed(() => {
  if (!originalData.value || !drawerProps.value.row) return false;

  // 深度比较当前配置与原始配置
  // 简单比较方法：转换为JSON字符串后比较
  const currentConfig = JSON.stringify(drawerProps.value.row);
  const originalConfig = JSON.stringify(originalData.value);

  return currentConfig !== originalConfig;
});

// 监听表单数据变化
watch(
  () => drawerProps.value.row,
  () => {
    // 当表单数据变化时，可以在这里添加额外的逻辑
    console.log("Form data changed, hasConfigChanged:", hasConfigChanged.value);
  },
  { deep: true }
);

defineExpose({
  acceptParams
});
</script>

<style lang="scss" scoped>
// 移动端响应式优化
@media screen and (width <= 768px) {
  :deep(.terminal-drawer) {
    .el-drawer__header {
      padding: 16px;
      font-size: 16px;
    }
    .el-drawer__body {
      padding: 16px;
    }
    .terminal-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          margin-bottom: 6px;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }
        .el-form-item__content {
          .el-input,
          .el-select,
          .el-time-picker {
            .el-input__wrapper,
            .el-select__wrapper {
              height: 40px;
              font-size: 14px;
            }
          }
          .el-switch {
            display: flex !important;
            align-items: center;
            .el-switch__core {
              order: 2;
              margin-left: 8px;
            }
            .el-switch__label {
              order: 1;
              font-size: 14px;
              &.el-switch__label--left {
                margin-right: 8px;
                margin-left: 0;
              }
              &.el-switch__label--right {
                order: 3;
                margin-right: 0;
                margin-left: 8px;
              }
            }
          }
          .el-radio-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
            .el-radio {
              margin-right: 0;
              .el-radio__label {
                font-size: 14px;
              }
            }
          }
          .el-text {
            font-size: 14px;
          }
        }
      }

      // Tabs 优化
      .el-tabs {
        .el-tabs__header {
          margin-bottom: 16px;
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              .el-tabs__item {
                padding: 8px 12px;
                font-size: 13px;
              }
            }
          }
        }
        .el-tabs__content {
          .el-tab-pane {
            padding: 0;
          }
        }
      }

      // 图片容器优化
      .el-space {
        justify-content: center;
        margin-bottom: 16px;
        .el-image {
          border: 1px solid var(--el-border-color);
          border-radius: 8px;
        }
      }
    }

    // Footer 按钮优化
    .el-drawer__footer {
      padding: 16px;
      text-align: center;
      border-top: 1px solid var(--el-border-color);
      .el-button {
        min-width: 80px;
        height: 40px;
        font-size: 14px;
        &:not(:last-child) {
          margin-right: 12px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.terminal-drawer) {
    .el-drawer__header {
      padding: 12px;
      font-size: 15px;
    }
    .el-drawer__body {
      padding: 12px;
    }
    .terminal-form {
      .el-form-item {
        margin-bottom: 14px;
        .el-form-item__label {
          margin-bottom: 4px;
          font-size: 13px;
        }
        .el-form-item__content {
          .el-input,
          .el-select,
          .el-time-picker {
            .el-input__wrapper,
            .el-select__wrapper {
              height: 36px;
              font-size: 13px;
            }
          }
          .el-switch {
            .el-switch__label {
              font-size: 13px;
            }
          }
          .el-radio-group {
            gap: 10px;
            .el-radio {
              .el-radio__label {
                font-size: 13px;
              }
            }
          }
          .el-text {
            font-size: 13px;
          }
        }
      }

      // Tabs进一步优化
      .el-tabs {
        .el-tabs__header {
          margin-bottom: 12px;
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              .el-tabs__item {
                padding: 6px 10px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    // Footer 按钮进一步优化
    .el-drawer__footer {
      padding: 12px;
      .el-button {
        min-width: 70px;
        height: 36px;
        font-size: 13px;
        &:not(:last-child) {
          margin-right: 10px;
        }
      }
    }
  }
}

@media screen and (width <= 350px) {
  :deep(.terminal-drawer) {
    .terminal-form {
      .el-form-item {
        .el-form-item__content {
          // 选择器组件垂直布局
          .el-select {
            width: 100%;
          }

          // 按钮组垂直排列
          .el-button-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            .el-button {
              width: 100%;
              margin: 0;
            }
          }
        }
      }
    }

    // Footer 按钮垂直排列
    .el-drawer__footer {
      .el-button {
        display: block;
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
