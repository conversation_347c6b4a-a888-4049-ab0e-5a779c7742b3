<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="isMobile ? '95vw' : '680px'"
    :title="`${drawerProps.title} ${t('device.ap')}`"
    @open="getApGroupRadioConfig"
    @close="onDrawerClose"
    class="ap-drawer"
  >
    <el-form
      ref="ruleFormRef"
      :label-width="isMobile ? '' : '120px'"
      :label-position="isMobile ? 'top' : 'left'"
      label-suffix=" :"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="true"
      class="ap-form"
    >
      <el-form-item :label="$t('device.apGroup')" label-width="160px">
        <el-select v-model="drawerProps.row.group_id" :placeholder="t('common.pleaseSelect')" clearable>
          <el-option v-for="item in apGroupList" :key="item.group_id" :label="item.alias" :value="item.group_id" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('device.deviceName')"
        label-width="160px"
        prop="alias"
        :rules="[
          { required: true, message: t('common.pleaseInput') + t('device.deviceName'), trigger: 'blur' },
          { max: 32, message: t('device.nameMaxLength'), trigger: 'blur' }
        ]"
      >
        <el-text v-if="!editName">{{ drawerProps.row?.alias }}</el-text>
        <el-link v-show="!drawerProps.isView" :icon="Edit" v-if="!editName" @click="editName = true">
          {{ $t("project.edit") }}
        </el-link>
        <el-input v-model="drawerProps.row.alias" v-if="editName" style="width: 60% !important" clearable>
          <template #append>
            <el-link
              v-show="!drawerProps.isView"
              :icon="Check"
              v-if="editName"
              @click="hasNameChanged && handleAliasSave()"
              :disabled="!hasNameChanged"
              :title="$t('common.save')"
            />
            <el-link
              v-show="!drawerProps.isView"
              :icon="Close"
              v-if="editName"
              @click="cancelAliasEdit"
              :style="{ marginRight: '8px' }"
              :title="$t('common.cancel')"
            />
          </template>
        </el-input>
      </el-form-item>
      <el-divider></el-divider>
      <el-tabs type="card" v-model="activeName">
        <el-tab-pane :label="t('device.deviceInfo')" name="first">
          <el-card :style="{ marginTop: '10px', marginBottom: '10px' }">
            <template #header>
              <div class="card-header">
                <span>{{ $t("device.deviceInfo") }}</span>
              </div>
            </template>
            <el-form-item :label="$t('common.alias')" prop="alias" label-position="left">
              <el-text>{{ drawerProps.row.alias }}</el-text>
            </el-form-item>
            <el-form-item :label="$t('device.mac')" prop="mac" label-position="left">
              <el-text>{{ drawerProps.row.mac }}</el-text>
            </el-form-item>
            <el-form-item :label="$t('device.model')" prop="model" label-position="left">
              <el-text>{{ drawerProps.row.model }}</el-text>
            </el-form-item>
            <el-form-item :label="$t('device.onlineStatusTip')" prop="online" label-position="left">
              <span>
                <el-tag :type="drawerProps.row.online === 1 ? 'success' : 'danger'" disable-transitions>
                  {{ formatOnlineStatus(drawerProps.row) }}
                </el-tag>
              </span>
            </el-form-item>
            <el-form-item :label="$t('device.ip')" prop="ip" label-position="left">
              <el-text>{{ drawerProps.row.ipaddr }}</el-text>
            </el-form-item>
            <el-form-item :label="$t('common.group')" prop="version" label-position="left">
              {{ t("terminal.group" + drawerProps.row.group_id) }}
            </el-form-item>
            <el-form-item :label="$t('common.version')" prop="version" label-position="left">
              {{ drawerProps.row.version }}
            </el-form-item>
          </el-card>
        </el-tab-pane>
        <el-tab-pane :label="$t('device.networkSettings')" name="second">
          <!--          {{ drawerProps.row }}-->
          <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
            <el-select
              v-if="drawerProps.row.radio0 && drawerProps.row.radio0.txpower"
              v-model="drawerProps.row.radio0.txpower"
              clearable
            >
              <el-option
                v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
                :key="percent"
                :label="`${percent}%`"
                :value="percent"
              />
            </el-select>
          </el-form-item>
          <el-tabs
            type="card"
            v-model="activeNetworkName"
            stretch
            v-if="
              (drawerProps.row && drawerProps.row?.radio0 && Object.keys(drawerProps.row?.radio0.wifi).length > 0) ||
              (drawerProps.row && drawerProps.row?.radio1 && Object.keys(drawerProps.row?.radio1.wifi).length > 0)
            "
          >
            <el-tab-pane label="2.4G" name="first">
              <!-- 2.4GHz -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-for="(radio0, index) in drawerProps.row?.radio0?.wifi || []"
                :key="index"
              >
                <!--                {{ drawerProps.row.radio0.wifi }}-->
                <el-header>
                  <el-form-item :label="$t('device.wifi24GHz')" label-position="left" label-width="160px">
                    <el-switch
                      v-model="radio0.disabled"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                      :active-value="0"
                      :inactive-value="1"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="radio0.disabled === 0">
                  <el-form-item
                    :label="$t('device.name')"
                    label-position="left"
                    label-width="160px"
                    :prop="`radio0.wifi.${index}.ssid`"
                    :rules="[
                      { required: true, message: t('device.ssidRequired'), trigger: 'blur' },
                      { max: 32, message: t('device.ssidMaxLength'), trigger: 'blur' }
                    ]"
                  >
                    <el-input v-model="radio0.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="radio0.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.key')"
                    label-position="left"
                    label-width="160px"
                    :prop="`radio0.wifi.${index}.key`"
                    :rules="[{ validator: validateKey, trigger: 'blur' }]"
                  >
                    <el-input type="password" v-model="radio0.key" show-password clearable />
                  </el-form-item>
                </el-main>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="5G" name="second">
              <!-- 5G -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-for="(radio1, index) in drawerProps.row?.radio1?.wifi || []"
                :key="index"
              >
                <el-header>
                  <el-form-item :label="$t('device.wifi5GHz')" label-position="left">
                    <el-switch
                      v-model="radio1.disabled"
                      active-color="#13ce66"
                      :active-value="0"
                      :inactive-value="1"
                      inactive-color="#ff4949"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="radio1.disabled === 0">
                  <el-form-item
                    :label="$t('device.name')"
                    label-position="left"
                    label-width="160px"
                    :prop="`radio1.wifi.${index}.ssid`"
                    :rules="[
                      { required: true, message: t('device.ssidRequired'), trigger: 'blur' },
                      { max: 32, message: t('device.ssidMaxLength'), trigger: 'blur' }
                    ]"
                  >
                    <el-input v-model="radio1.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="radio1.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item
                    :label="$t('device.key')"
                    label-position="left"
                    label-width="160px"
                    :prop="`radio1.wifi.${index}.key`"
                    :rules="[{ validator: validateKey, trigger: 'blur' }]"
                  >
                    <el-input type="password" v-model="radio1.key" show-password clearable />
                  </el-form-item>
                </el-main>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane :label="t('device.systemSettings')" name="third">
          <el-form-item
            :label="t('device.ledConfiguration')"
            label-position="left"
            v-if="drawerProps.row.ledoff !== undefined"
            label-width="160px"
          >
            <!--            {{ drawerProps.row }}-->
            <el-select v-model="drawerProps.row.ledoff" :placeholder="t('common.pleaseSelect')" clearable>
              <el-option :label="t('device.on')" :value="0"></el-option>
              <el-option :label="t('device.off')" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :disabled="!hasConfigChanged" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="ApDrawer">
import { ref, computed, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { Edit, Check, Close } from "@element-plus/icons-vue";
import { formatOnlineStatus } from "@/api/modules/terminal";
import { getDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { Project } from "@/api/interface/project";
import { apGroupList } from "@/api/interface/apGroupDrawer";
import { getDiff } from "@/utils/diff";

const { t } = useI18n();

const validateKey = (rule: any, value: any, callback: any) => {
  if (value) {
    if (value.length < 8 || value.length > 64) {
      callback(new Error(t("device.key") + t("device.keyLengthTip")));
    } else if (!/^[\w\.-_~`!@#$%^&*()+\?><,\|\\\[\]\/;':" ]+$/.test(value)) {
      callback(new Error(t("device.keyRuleAscii")));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

const activeName = ref<"first" | "second" | "third">("first"); // 底层tabs默认选中第一个
const activeNetworkName = ref<"first" | "second">("first"); // 底层tabs默认选中第一个

const drawerVisible = ref(false);

const editName = ref(false);
const originalName = ref("");

// 保存原始数据的副本，用于比较是否有变化
const originalData = ref<Partial<DeviceData> | null>(null);

interface WifiConfig {
  hidden: number;
  disabled: number;
  ssid: string;
  key: string;
  group_id?: number;
}

interface RadioConfig {
  wifi: WifiConfig[];
  txpower: number;
  channel: number;
}

interface DeviceData {
  private: number;
  group_id: number;
  station: number;
  alias: string;
  online: number;
  model: string;
  ipaddr: string;
  version: string;
  ledoff: number;
  mac: string;
  radio0: RadioConfig;
  radio1: RadioConfig;
  apList?: any[];
}

const onDrawerClose = () => {
  editName.value = false;
  activeName.value = "first";
  activeNetworkName.value = "first";
  originalData.value = null; // 关闭抽屉时清空原始数据
};

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<DeviceData>;
  parentRow: Partial<Project.ResDeviceList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {
    radio0: {
      txpower: 0,
      wifi: [],
      channel: 0
    },
    radio1: {
      txpower: 0,
      wifi: [],
      channel: 0
    }
  },
  parentRow: {}
});
// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
  originalData.value = null; // 重置原始数据，等待数据加载完成后再保存
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    // 只取变化的部分
    const diff = getDiff(originalData.value, drawerProps.value.row);
    if (!diff) {
      ElMessage.warning(t("common.noConfigChanges"));
      return;
    }
    try {
      // 处理 apList，每个对象加 mac 字段
      let apList = diff.apList;
      if (Array.isArray(apList)) {
        apList = apList.map((ap: any) => ({
          ...ap,
          mac: drawerProps.value.row.mac
        }));
      }
      const params = {
        scope: {
          deviceId: drawerProps.value.parentRow?.deviceId || "",
          row: drawerProps.value.parentRow || {}
        },
        row: {
          ...diff,
          group_id: drawerProps.value.row.group_id,
          mac: drawerProps.value.row.mac,
          ...(apList ? { apList } : {})
        }
      };
      const response = await drawerProps.value.api!(params);
      console.log("API 响应数据:", response);
      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg });
        return;
      }
      ElMessage.success({ message: t("common.operationSuccess") });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

//  drawerProps.row.private === 1,如果AP有私有配置，则显示私有配置，否则显示分组的配置
const getApGroupRadioConfig = async () => {
  if (drawerProps.value.row.private === 0) {
    console.log("drawerProps.value.parentRow:", drawerProps.value.parentRow);
    const params = {
      cmd: 10,
      deviceId: drawerProps.value.parentRow.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: ["apGroup"]
      }
    };
    const response = await getDeviceConfigJwe(params);
    console.log("getApGroupRadioConfig response:", response);
    if (response && response.data) {
      const apGroup = response.data.system.apGroup;
      // 查找与 drawerProps.value.row.group_id 匹配的 apGroup 对象
      const matchedGroup = apGroup.find(group => group.group_id === drawerProps.value.row.group_id);

      // 如果找到了匹配的组，则赋值 radio0 和 radio1
      if (matchedGroup) {
        drawerProps.value.row.radio0 = matchedGroup.radio0;
        drawerProps.value.row.radio1 = matchedGroup.radio1;
      }
    }
  }

  // 保存原始数据的深拷贝，用于后续比较变化
  originalData.value = JSON.parse(JSON.stringify(drawerProps.value.row));
};

// 计算属性：检测配置是否有变化
const hasConfigChanged = computed(() => {
  if (!originalData.value || !drawerProps.value.row) return false;

  // 深度比较当前配置与原始配置
  // 简单比较方法：转换为JSON字符串后比较
  const currentConfig = JSON.stringify(drawerProps.value.row);
  const originalConfig = JSON.stringify(originalData.value);

  return currentConfig !== originalConfig;
});

// 计算属性：检测名称是否有变化
const hasNameChanged = computed(() => {
  if (!editName.value) return false;
  return drawerProps.value.row.alias !== originalName.value;
});

const cancelAliasEdit = () => {
  editName.value = false;
  drawerProps.value.row.alias = originalName.value;
  ruleFormRef.value?.clearValidate("alias");
};

const handleAliasSave = () => {
  ruleFormRef.value?.validateField("alias", async isValid => {
    if (!isValid) return;
    // 直接传递 row: { ...drawerProps.value.row }
    const params = {
      scope: {
        deviceId: drawerProps.value.parentRow?.deviceId || "",
        row: {}
      },
      row: {
        ...drawerProps.value.row
      }
    };
    try {
      const response = await drawerProps.value.api?.(params);
      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg });
        return;
      }
      ElMessage.success({ message: t("common.operationSuccess") });
      editName.value = false;
      originalName.value = drawerProps.value.row.alias;
      if (drawerProps.value.getTableList) {
        drawerProps.value.getTableList();
      }
    } catch (error) {
      console.log(error);
    }
  });
};

// 打开抽屉时保存原始名称
watch(
  () => drawerVisible.value,
  val => {
    if (val) {
      originalName.value = drawerProps.value.row.alias || "";
    }
  }
);

defineExpose({
  acceptParams
});
</script>
<style scoped lang="scss">
.device-img {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}
.sw-port-list {
  display: flex; /* 设置为横向排列 */
  flex-wrap: wrap; /* 如果元素过多，换行显示 */
  gap: 10px; /* 设置每个 item 之间的间距 */
  padding: 0; /* 去掉默认的 padding */
  margin: 0; /* 去掉默认的 margin */
}
.sw-port-tag {
  box-sizing: border-box; /* 包括边框和内边距在尺寸内 */
  display: flex; /* 设置为 flex 容器 */
  flex-direction: column; /* 垂直排列图标和名称 */
  align-items: center; /* 图标和名称居中 */
  justify-content: center; /* 纵向居中对齐 */
  width: 50px; /* 固定宽度 */
  height: 65px; /* 固定高度 */
  padding: 5px; /* 内边距调整为 10px */
  margin: 1px; /* 设置间距 */
  background-color: #f9f9f9; /* 默认背景色 */
  border: 1px solid transparent; /* 默认边框透明 */
  border-radius: 5px; /* 可选，增加圆角 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}
.sw-port-list li {
  text-align: center; /* 文字居中 */
}
.sw-port-example {
  display: flex;
  flex-shrink: 0; /* 防止内容缩小导致换行 */
  align-items: center;
  justify-content: flex-start;
  margin-right: 15px; /* 给每个项增加右边距，确保之间有间隔 */
  white-space: nowrap; /* 确保文字不换行 */
}
.port-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px; /* 图标与文字之间的间距 */
}
.port-text {
  line-height: 20px; /* 保证文字与图标垂直居中 */
  white-space: nowrap; /* 确保文字不换行 */
}

/* 特殊样式 */
.selected-port {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff; /* 设置边框宽度为 1px */
  border-radius: 5px; /* 可选，增加圆角效果 */
}
</style>
