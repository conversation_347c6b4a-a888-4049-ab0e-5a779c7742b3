<template>
  <div class="guide-simple-test">
    <div class="card content-box">
      <h1>指南系统简单测试</h1>
      
      <div class="test-controls">
        <el-button type="primary" @click="startSimpleGuide" size="large">
          启动简单指南
        </el-button>
        <el-button type="success" @click="checkElements" size="large">
          检查元素
        </el-button>
        <el-button type="warning" @click="debugGuide" size="large">
          调试信息
        </el-button>
      </div>

      <div class="test-elements">
        <!-- 模拟项目选择器 -->
        <div class="tree-filter test-element" id="testTreeFilter">
          <h3>项目选择器</h3>
          <p>这是模拟的项目选择器组件</p>
        </div>

        <!-- 模拟标签页 -->
        <div class="tabs-header test-element" id="testTabsHeader">
          <h3>标签页头部</h3>
          <div class="tab-items">
            <span class="tab-item active">网络拓扑</span>
            <span class="tab-item">项目目录</span>
          </div>
        </div>

        <!-- 模拟拓扑图内容 -->
        <div class="topology-tab-content test-element" id="testTopologyContent">
          <h3>拓扑图内容</h3>
          <div class="topology-mockup">
            <div class="node">设备1</div>
            <div class="node">设备2</div>
            <div class="node">设备3</div>
          </div>
        </div>

        <!-- 模拟设备列表 -->
        <div class="table-box test-element" id="testTableBox">
          <h3>设备列表</h3>
          <div class="device-item">路由器 - ***********</div>
          <div class="device-item">交换机 - ***********</div>
          <div class="device-item">AP - ***********</div>
        </div>
      </div>

      <div class="debug-info" v-if="debugInfo">
        <h3>调试信息</h3>
        <pre>{{ JSON.stringify(debugInfo, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GuideSimpleTest">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useGuide } from "@/hooks/useGuide";
import { generateDebugReport, checkGuideElements } from "@/utils/guideDebug";

const { startCustomGuide, startProjectGuide } = useGuide();
const debugInfo = ref(null);

// 启动简单指南
const startSimpleGuide = async () => {
  console.log("Starting simple guide...");
  
  const simpleSteps = [
    {
      element: "body",
      popover: {
        title: "欢迎使用指南系统",
        description: "这是一个简单的指南演示，将引导您了解页面的主要功能",
        side: "bottom"
      }
    },
    {
      element: "#testTreeFilter",
      popover: {
        title: "项目选择器",
        description: "在这里选择您要管理的项目。可以切换不同的项目进行管理",
        side: "right"
      }
    },
    {
      element: "#testTabsHeader",
      popover: {
        title: "功能标签页",
        description: "点击不同的标签页可以切换到不同的功能视图",
        side: "bottom"
      }
    },
    {
      element: "#testTopologyContent",
      popover: {
        title: "网络拓扑图",
        description: "这里显示网络设备的连接关系，可以直观地查看网络结构",
        side: "top"
      }
    },
    {
      element: "#testTableBox",
      popover: {
        title: "设备管理列表",
        description: "这里列出了所有的网络设备，可以进行设备的管理和配置",
        side: "top"
      }
    }
  ];

  try {
    await startCustomGuide(simpleSteps, {
      allowClose: true,
      animate: true,
      opacity: 0.75,
      padding: 10
    });
    
    ElMessage.success("指南启动成功！");
  } catch (error) {
    console.error("Error starting simple guide:", error);
    ElMessage.error("指南启动失败：" + error.message);
  }
};

// 检查元素
const checkElements = () => {
  console.log("Checking elements...");
  
  const elements = checkGuideElements();
  const foundCount = Object.values(elements).filter(Boolean).length;
  const totalCount = Object.keys(elements).length;
  
  ElMessage.info(`找到 ${foundCount}/${totalCount} 个指南元素`);
  
  console.table(elements);
};

// 调试指南
const debugGuide = () => {
  console.log("Generating debug info...");
  
  try {
    debugInfo.value = generateDebugReport();
    ElMessage.success("调试信息已生成，请查看页面下方");
  } catch (error) {
    console.error("Error generating debug info:", error);
    ElMessage.error("生成调试信息失败：" + error.message);
  }
};
</script>

<style scoped lang="scss">
.guide-simple-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-elements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-element {
  background: var(--el-bg-color-page);
  border: 2px solid var(--el-border-color);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }
  
  h3 {
    margin: 0 0 15px 0;
    color: var(--el-color-primary);
    font-size: 1.2rem;
  }
  
  p {
    margin: 0;
    color: var(--el-text-color-regular);
  }
}

.tab-items {
  display: flex;
  gap: 10px;
  
  .tab-item {
    padding: 8px 16px;
    background: var(--el-color-primary-light-9);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: var(--el-color-primary);
      color: white;
    }
    
    &:hover:not(.active) {
      background: var(--el-color-primary-light-7);
    }
  }
}

.topology-mockup {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  
  .node {
    background: var(--el-color-success);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    text-align: center;
  }
}

.device-item {
  padding: 8px 12px;
  background: var(--el-color-info-light-9);
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid var(--el-color-info);
}

.debug-info {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 20px;
  
  h3 {
    margin-top: 0;
    color: var(--el-color-warning);
  }
  
  pre {
    background: var(--el-fill-color-light);
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
  }
}

@media (max-width: 768px) {
  .test-elements {
    grid-template-columns: 1fr;
  }
  
  .test-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
