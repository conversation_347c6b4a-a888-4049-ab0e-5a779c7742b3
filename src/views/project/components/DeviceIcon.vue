<template>
  <img :src="iconSrc" class="device-img" :alt="deviceType" />
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  deviceType: {
    type: String,
    required: true
  }
});

const iconSrc = computed(() => {
  switch (props.deviceType) {
    case "route":
      return new URL("@/assets/images/router_icon.png", import.meta.url).href;
    case "switch":
      return new URL("@/assets/images/switch_icon.png", import.meta.url).href;
    case "bridge":
      return new URL("@/assets/images/bridge_icon.png", import.meta.url).href;
    case "ap":
      return new URL("@/assets/images/ap_icon.png", import.meta.url).href;
    case "ac":
      return new URL("@/assets/images/ac_icon.png", import.meta.url).href;
    case "repeater":
      return new URL("@/assets/images/zhongji_icon.png", import.meta.url).href;
    default:
      return "";
  }
});
</script>
