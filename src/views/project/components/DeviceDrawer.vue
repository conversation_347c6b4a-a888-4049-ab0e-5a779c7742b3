<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :size="drawerSize"
    :title="`${drawerProps.title} ${t('device.device')}`"
  >
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="true"
      label-position="left"
    >
      <el-form-item prop="deviceId" :required="drawerProps.title === t('device.bind')">
        <template #label>
          <span>
            {{ $t("device.deviceId") }}
            <el-tooltip :content="$t('device.deviceIdTooltip')" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-input
          v-model="drawerProps.row.deviceId"
          :placeholder="$t('device.deviceIdPlaceholder')"
          v-if="drawerProps.title === t('device.bind')"
          clearable
        ></el-input>
        <el-text v-else>{{ drawerProps.row.deviceId }}</el-text>
      </el-form-item>
      <el-form-item prop="password" v-if="drawerProps.title === t('device.bind')">
        <template #label>
          <span>
            {{ $t("device.password") }}
            <el-tooltip :content="$t('device.passwordTooltip')" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-input
          v-model="drawerProps.row.password"
          type="password"
          :placeholder="$t('device.passwordPlaceholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('device.model')" prop="deviceModel" v-if="drawerProps.title !== t('device.bind')">
        <el-text>{{ drawerProps.row.deviceModel }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.deviceName')" v-if="drawerProps.title !== t('device.bind')">
        <el-input v-model="drawerProps.row.deviceName" v-if="drawerProps.title === t('common.edit')" clearable></el-input>
        <el-text v-else>{{ drawerProps.row.deviceName }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.type')" prop="deviceType" v-if="drawerProps.title !== t('device.bind')">
        <el-select
          v-model="drawerProps.row.deviceType"
          :placeholder="$t('device.typePlaceholder')"
          v-if="drawerProps.title === t('device.bind')"
          clearable
        >
          <el-option
            v-for="item in deviceTypes"
            :key="item.configCode"
            :label="isChinese ? item.configDesc : item.attribute || item.configDesc"
            :value="item.configCode"
          ></el-option>
        </el-select>
        <el-text v-else>{{ deviceTypeLabel }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.bootTime')" v-if="drawerProps.isView">
        <el-text>{{ formattedBootTime }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.mac')" prop="mac" v-if="drawerProps.title !== t('device.bind')">
        <el-text>{{ drawerProps.row.mac }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.ip')" prop="ip" v-if="drawerProps.title !== t('device.bind')">
        <el-text>{{ drawerProps.row.ipaddr }}</el-text>
      </el-form-item>
      <el-form-item :label="$t('device.status')" prop="address" v-if="drawerProps.isView">
        <el-text :style="{ color: statusTagType === 'danger' ? 'red' : 'green' }">
          {{ statusLabel }}
        </el-text>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :disabled="!hasConfigChanged" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="DeviceDrawer">
import { ref, reactive, computed, inject, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import type { Project } from "@/api/interface/project";
import { deviceStatusEnum } from "@/api/modules/project";
import { useGlobalStore } from "@/stores/modules/global";
import {} from /* getDeviceType, getDeviceConfig, saveDeviceConfig, bindDevice, updateDevice */ "@/api/modules/project";
// import {端口映射API } from "@/api/modules/portForward";
// import {下发配置API } from "@/api/modules/deviceConfig";
// 引入新增的API
// import { saveDeviceGroup } from "@/api/modules/rnet";

const { t } = useI18n();
const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh"); // 使用computed使其响应语言变化

const rules = reactive({
  deviceId: [{ required: true, message: t("device.deviceIdPlaceholder") }],
  password: [{ required: true, message: t("device.passwordPlaceholder") }],
  deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
  deviceType: [{ required: false, message: t("device.typePlaceholder") }],
  mac: [{ required: false, message: t("device.macPlaceholder") }],
  ip: [{ required: false, message: t("device.ipPlaceholder") }]
});

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Project.ResDeviceList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  projectId?: string;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 保存原始数据的副本，用于比较是否有变化
const originalData = ref(null);

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;

  // 保存原始数据的深拷贝，用于后续比较变化
  originalData.value = JSON.parse(JSON.stringify(params.row));
};

// 关闭抽屉时清空原始数据
watch(drawerVisible, newVal => {
  if (!newVal) {
    originalData.value = null;
  }
});

const deviceTypeLabel = computed(() => {
  const deviceTypeItem = deviceTypes.value.find(item => item.configCode === drawerProps.value.row.deviceType);
  return deviceTypeItem
    ? isChinese.value
      ? deviceTypeItem.configDesc
      : deviceTypeItem.attribute || deviceTypeItem.configDesc
    : drawerProps.value.row.deviceType;
});

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  const form = ruleFormRef.value;
  if (!form) return;
  form.validate(async valid => {
    if (!valid) return;
    try {
      const params = {
        id: drawerProps.value.row.id,
        deviceId: drawerProps.value.row.deviceId,
        password: drawerProps.value.row.password,
        deviceName: drawerProps.value.row.deviceName,
        groupId: drawerProps.value.projectId
      };
      const response = await drawerProps.value.api!(params);
      console.log("API 响应数据:", response);
      if (!response || (response.code !== "200" && response.code !== 200)) {
        ElMessage.error({ message: response.msg });
        return;
      } else {
        // 这里可以根据实际需要处理响应数据
        ElMessage.success({ message: t("common.bindingSuccess") + "！" });
      }

      // // 如果是绑定操作，则接着调用 saveDeviceGroup
      // if (drawerProps.value.title === t("device.bind")) {
      //   ElMessage.success({ message: t("common.bindingSuccess") + "！" });
      //   // Cast drawerProps.value to DrawerProps to access projectId
      //   const currentProps = drawerProps.value as DrawerProps;
      //   if (currentProps.projectId && params.deviceId) {
      //     try {
      //       const saveGroupRes = await saveDeviceGroup({ groupId: currentProps.projectId, deviceId: params.deviceId });
      //       if (!saveGroupRes || saveGroupRes.code !== "200" /* && saveGroupRes.code !== 200 */) {
      //         ElMessage.error({ message: saveGroupRes.msg || t("device.saveToGroupFailed") });
      //         // 注意：这里绑定成功了，但保存到分组失败，需要用户知晓
      //       } else {
      //         ElMessage.success({ message: t("device.saveToGroupSuccess") });
      //       }
      //     } catch (groupError) {
      //       console.log("Save to group error:", groupError);
      //       ElMessage.error({ message: t("device.saveToGroupFailed") });
      //     }
      //   } else {
      //     console.warn("Missing projectId or deviceId for saveDeviceGroup");
      //     ElMessage.warning({ message: t("device.saveToGroupSkippedMissingInfo") });
      //   }
      // } else if (drawerProps.value.title === t("common.edit")) {
      //   ElMessage.success({ message: t("common.modifySuccess") + "！" });
      // }
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
      ElMessage.error({ message: t("common.operationFailed") });
    }
  });
};

// 格式化后的开机时间
const formattedBootTime = computed(() => {
  const bootTimeInSeconds = drawerProps.value.row.bootTime;
  if (!bootTimeInSeconds) return "";

  const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
  const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
  const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
  const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
  const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
  const seconds = bootTimeInSeconds % 60;

  // 以数组的形式存储各部分
  const timeParts: string[] = [];

  if (years > 0) {
    timeParts.push(`${years} ${isChinese.value ? "年" : "yr"}`);
  }
  if (months > 0) {
    timeParts.push(`${months} ${isChinese.value ? "月" : "mo"}`);
  }
  if (days > 0) {
    timeParts.push(`${days} ${isChinese.value ? "天" : "d"}`);
  }
  if (hours > 0) {
    timeParts.push(`${hours} ${isChinese.value ? "小时" : "h"}`);
  }
  if (minutes > 0) {
    timeParts.push(`${minutes} ${isChinese.value ? "分钟" : "min"}`);
  }
  if (seconds > 0 || timeParts.length === 0) {
    timeParts.push(`${seconds} ${isChinese.value ? "秒" : "s"}`);
  }

  return timeParts.join(" ");
});

const status = computed(() => Number(drawerProps.value.row.status));

const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => item.value === Number(status.value));
  return statusItem ? statusItem.label : "";
});

const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => item.value === Number(status.value));
  return statusItem ? statusItem.tagType : "";
});

// 存储设备类型列表
// const deviceTypes = ref<Project.ResConfigList[]>([]);
const deviceTypes = inject("deviceTypes", ref<Project.ResConfigList[]>([])); // 提供默认值为 ref([])

// 计算属性：检测配置是否有变化
const hasConfigChanged = computed(() => {
  if (!originalData.value || !drawerProps.value.row) return false;

  // 深度比较当前配置与原始配置
  // 简单比较方法：转换为JSON字符串后比较
  const currentConfig = JSON.stringify(drawerProps.value.row);
  const originalConfig = JSON.stringify(originalData.value);

  return currentConfig !== originalConfig;
});

// 响应式drawer宽度
const drawerSize = computed(() => {
  if (window.innerWidth <= 320) return "98vw";
  if (window.innerWidth <= 480) return "95vw";
  if (window.innerWidth <= 768) return "80vw";
  return "450px";
});

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
@media screen and (width <= 768px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 14px 18px;
      font-size: 15px;
      border-bottom: 1px solid #ebeef5;
    }
    .el-drawer__body {
      padding: 16px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 18px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 14px;
          .el-input {
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
              line-height: 1.4;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-text {
            font-size: 14px;
            line-height: 1.5;
            color: #606266;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 12px 16px;
      text-align: center;
      border-top: 1px solid #ebeef5;
      .el-button {
        min-width: 80px;
        padding: 8px 20px;
        font-size: 14px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 12px 16px;
      font-size: 14px;
    }
    .el-drawer__body {
      padding: 12px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 14px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 5px !important;
          font-size: 13px;
          font-weight: 500;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 13px;
          .el-input {
            .el-input__inner {
              padding: 8px 10px;
              font-size: 13px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 8px 10px;
              font-size: 13px;
            }
          }
          .el-text {
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 10px 12px;
      .el-button {
        min-width: 70px;
        padding: 7px 16px;
        font-size: 13px;
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 10px 12px;
      font-size: 13px;
    }
    .el-drawer__body {
      padding: 8px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          padding: 0 0 4px !important;
          font-size: 12px;
        }
        .el-form-item__content {
          font-size: 12px;
          .el-input {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 12px;
            }
          }
          .el-select {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 12px;
            }
          }
          .el-text {
            font-size: 12px;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 8px 10px;
      .el-button {
        min-width: 60px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}
</style>
