<template>
  <div class="guide-final-test">
    <div class="card content-box">
      <h1>🎯 最终指南系统测试</h1>
      
      <div class="test-info">
        <h2>系统页面结构分析</h2>
        <div class="page-list">
          <div class="page-item">
            <h3>📊 首页 (/home/<USER>/h3>
            <p>系统概览和快速操作入口</p>
            <el-button @click="testHomePage" size="small">测试首页指南</el-button>
          </div>
          
          <div class="page-item">
            <h3>📁 项目管理 (/project/index)</h3>
            <p>包含拓扑图和设备管理的主要页面</p>
            <el-button @click="testProjectPage" size="small">测试项目指南</el-button>
          </div>
          
          <div class="page-item">
            <h3>🌐 远程网络 (/rnet/index)</h3>
            <p>远程网络设备管理和拓扑显示</p>
            <el-button @click="testRnetPage" size="small">测试远程网络指南</el-button>
          </div>
          
          <div class="page-item">
            <h3>💻 终端管理 (/terminal/index)</h3>
            <p>设备终端连接和管理</p>
            <el-button @click="testTerminalPage" size="small">测试终端指南</el-button>
          </div>
        </div>
      </div>

      <div class="current-page-info">
        <h2>当前页面信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <strong>当前路径:</strong> {{ currentPath }}
          </div>
          <div class="info-item">
            <strong>页面类型:</strong> {{ pageType }}
          </div>
          <div class="info-item">
            <strong>检测到的元素:</strong> {{ detectedElements.length }} 个
          </div>
        </div>
      </div>

      <div class="test-controls">
        <h2>测试控制</h2>
        <div class="control-buttons">
          <el-button type="primary" @click="startSmartGuide" size="large">
            🤖 启动智能指南
          </el-button>
          <el-button type="success" @click="detectPageElements" size="large">
            🔍 检测页面元素
          </el-button>
          <el-button type="warning" @click="showCurrentPageGuide" size="large">
            📖 显示当前页面指南
          </el-button>
        </div>
      </div>

      <div class="detection-results" v-if="showResults">
        <h2>元素检测结果</h2>
        <div class="results-list">
          <div 
            v-for="element in detectedElements" 
            :key="element.selector"
            class="result-item"
            :class="{ found: element.found }"
          >
            <span class="selector">{{ element.selector }}</span>
            <span class="status">{{ element.found ? '✅' : '❌' }}</span>
            <span class="description">{{ element.description }}</span>
          </div>
        </div>
      </div>

      <div class="guide-steps" v-if="currentSteps.length > 0">
        <h2>当前页面指南步骤</h2>
        <div class="steps-list">
          <div 
            v-for="(step, index) in currentSteps" 
            :key="index"
            class="step-item"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4>{{ step.popover.title }}</h4>
              <p>{{ step.popover.description }}</p>
              <small>目标元素: {{ step.element }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GuideFinalTest">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useGuide } from "@/hooks/useGuide";

const { 
  startAutoGuide, 
  startProjectGuide,
  isGuideActive,
  currentGuideType 
} = useGuide();

const currentPath = ref(window.location.pathname);
const detectedElements = ref([]);
const showResults = ref(false);
const currentSteps = ref([]);

// 页面类型判断
const pageType = computed(() => {
  if (currentPath.value.includes('/project')) return '项目管理页面';
  if (currentPath.value.includes('/rnet')) return '远程网络页面';
  if (currentPath.value.includes('/terminal')) return '终端管理页面';
  if (currentPath.value.includes('/home')) return '首页';
  return '测试页面';
});

// 需要检测的页面元素
const elementsToDetect = [
  { selector: '.tree-filter', description: '项目选择器' },
  { selector: '.tabs-header', description: '标签页头部' },
  { selector: '.topology-tab-content', description: '拓扑图内容' },
  { selector: '.project-tab-content', description: '项目目录内容' },
  { selector: '.card-space', description: '设备卡片区域' },
  { selector: '.el-table', description: '数据表格' },
  { selector: '#collapseIcon', description: '侧边栏折叠按钮' },
  { selector: '#breadcrumb', description: '面包屑导航' },
  { selector: '#language', description: '语言切换' },
  { selector: '#darkMode', description: '暗黑模式' },
  { selector: '#themeSetting', description: '主题设置' },
  { selector: '#guide', description: '指南按钮' },
  { selector: '.floating-guide-button', description: '浮动指南按钮' }
];

// 启动智能指南
const startSmartGuide = async () => {
  try {
    await startAutoGuide({
      allowClose: true,
      animate: true,
      opacity: 0.75,
      padding: 10
    });
    ElMessage.success('智能指南启动成功！');
  } catch (error) {
    console.error('Error starting smart guide:', error);
    ElMessage.error('智能指南启动失败：' + error.message);
  }
};

// 检测页面元素
const detectPageElements = () => {
  const results = elementsToDetect.map(item => ({
    ...item,
    found: !!document.querySelector(item.selector)
  }));
  
  detectedElements.value = results;
  showResults.value = true;
  
  const foundCount = results.filter(item => item.found).length;
  ElMessage.info(`检测完成：找到 ${foundCount}/${results.length} 个元素`);
};

// 显示当前页面指南步骤
const showCurrentPageGuide = () => {
  // 这里模拟获取当前页面的指南步骤
  const mockSteps = [
    {
      element: "body",
      popover: {
        title: "欢迎使用系统",
        description: "这是当前页面的指南介绍"
      }
    }
  ];
  
  currentSteps.value = mockSteps;
  ElMessage.success('已显示当前页面指南步骤');
};

// 测试不同页面的指南
const testHomePage = () => {
  ElMessage.info('首页指南功能：显示系统概览和快速操作');
};

const testProjectPage = async () => {
  try {
    await startProjectGuide({
      allowClose: true,
      animate: true,
      opacity: 0.75
    });
    ElMessage.success('项目页面指南启动成功！');
  } catch (error) {
    ElMessage.error('项目页面指南启动失败：' + error.message);
  }
};

const testRnetPage = () => {
  ElMessage.info('远程网络指南功能：显示网络拓扑和设备管理');
};

const testTerminalPage = () => {
  ElMessage.info('终端管理指南功能：显示设备终端连接管理');
};

onMounted(() => {
  // 自动检测页面元素
  detectPageElements();
});
</script>

<style scoped lang="scss">
.guide-final-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  margin-bottom: 30px;
  
  .page-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
  }
  
  .page-item {
    padding: 20px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background: var(--el-bg-color-page);
    
    h3 {
      margin: 0 0 10px 0;
      color: var(--el-color-primary);
    }
    
    p {
      margin: 0 0 15px 0;
      color: var(--el-text-color-regular);
    }
  }
}

.current-page-info {
  margin-bottom: 30px;
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
  }
  
  .info-item {
    padding: 15px;
    background: var(--el-color-info-light-9);
    border-radius: 6px;
    border-left: 4px solid var(--el-color-info);
  }
}

.test-controls {
  margin-bottom: 30px;
  
  .control-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 15px;
  }
}

.detection-results {
  margin-bottom: 30px;
  
  .results-list {
    margin-top: 15px;
  }
  
  .result-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    background: var(--el-bg-color-page);
    
    &.found {
      border-left: 4px solid var(--el-color-success);
    }
    
    &:not(.found) {
      border-left: 4px solid var(--el-color-danger);
      opacity: 0.6;
    }
    
    .selector {
      font-family: monospace;
      font-weight: bold;
      min-width: 200px;
    }
    
    .status {
      font-size: 18px;
      min-width: 30px;
    }
    
    .description {
      color: var(--el-text-color-regular);
    }
  }
}

.guide-steps {
  .steps-list {
    margin-top: 15px;
  }
  
  .step-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background: var(--el-bg-color-page);
    
    .step-number {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: var(--el-color-primary);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      flex-shrink: 0;
    }
    
    .step-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        color: var(--el-color-primary);
      }
      
      p {
        margin: 0 0 8px 0;
        line-height: 1.5;
      }
      
      small {
        color: var(--el-text-color-secondary);
        font-family: monospace;
      }
    }
  }
}

h2 {
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary-light-8);
  padding-bottom: 5px;
  margin-bottom: 15px;
}

@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
  }
  
  .page-list {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
