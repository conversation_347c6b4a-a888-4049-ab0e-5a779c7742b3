<template>
  <div class="card filter">
    <h4 v-if="title" class="title sle" style="text-align: center">
      {{ title }}
    </h4>
    <div class="search">
      <el-input v-model="filterText" :placeholder="t('common.filterTip')" clearable />
      <el-dropdown trigger="click">
        <el-icon size="20"><More /></el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <!--            <el-dropdown-item @click="toggleTreeNodes(true)">{{ $t("common.add") }}</el-dropdown-item>-->
            <!--            <el-dropdown-item @click="toggleTreeNodes(false)">折叠全部</el-dropdown-item>-->
            <slot name="dropdown" />
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-scrollbar :style="{ height: title ? `calc(100% - 95px)` : `calc(100% - 56px)` }">
      <el-tree
        ref="treeRef"
        default-expand-all
        :node-key="id"
        :data="multiple ? treeData : treeAllData"
        :show-checkbox="multiple"
        :check-strictly="false"
        :current-node-key="!multiple ? selected : ''"
        :highlight-current="!multiple"
        :expand-on-click-node="false"
        :check-on-click-node="multiple"
        :props="defaultProps"
        :filter-node-method="filterNode"
        :default-checked-keys="multiple ? selected : []"
        @node-click="handleNodeClick"
        @check="handleCheckChange"
      >
        <template #default="scope">
          <span class="el-tree-node__label">
            <slot :row="scope">
              {{ scope.node.label }}
            </slot>
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts" name="TreeFilter">
import { ref, watch, onBeforeMount, nextTick } from "vue";
import { ElTree } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// 接收父组件参数并设置默认值
interface TreeFilterProps {
  requestApi?: (data?: any) => Promise<any>; // 请求分类数据的 api ==> 非必传
  data?: { [key: string]: any }[]; // 分类数据，如果有分类数据，则不会执行 api 请求 ==> 非必传
  title?: string; // treeFilter 标题 ==> 非必传
  id?: string; // 选择的id ==> 非必传，默认为 “id”
  label?: string; // 显示的label ==> 非必传，默认为 “label”
  multiple?: boolean; // 是否为多选 ==> 非必传，默认为 false
  defaultValue?: any; // 默认选中的值 ==> 非必传
}
const props = withDefaults(defineProps<TreeFilterProps>(), {
  id: "id",
  label: "label",
  multiple: false
});

const defaultProps = {
  children: "children",
  label: props.label
};

const treeRef = ref<InstanceType<typeof ElTree>>();
const treeData = ref<{ [key: string]: any }[]>([]);
const treeAllData = ref<{ [key: string]: any }[]>([]);

const selected = ref();
const setSelected = () => {
  if (props.multiple) selected.value = Array.isArray(props.defaultValue) ? props.defaultValue : [props.defaultValue];
  else selected.value = typeof props.defaultValue === "string" ? props.defaultValue : "";
};

onBeforeMount(async () => {
  setSelected();
  if (props.requestApi) {
    const { data } = await props.requestApi!();
    treeData.value = data;
    treeAllData.value = [...data];
    await nextTick(() => {
      // 设置默认选中第一个节点
      const firstNode = treeData.value[0];
      if (firstNode) {
        treeRef.value!.setCurrentKey(firstNode[props.id]);
        // 不再自动触发 node-click 事件，避免重复请求
        // handleNodeClick(firstNode);
      }
    });
  }
});

// 使用 nextTick 防止打包后赋值不生效，开发环境是正常的
watch(
  () => props.defaultValue,
  () => nextTick(() => setSelected()),
  { deep: true, immediate: true }
);

watch(
  () => props.data,
  () => {
    if (props.data?.length) {
      treeData.value = props.data;
      treeAllData.value = [{ id: "", [props.label]: "全部" }, ...props.data];
    }
  },
  { deep: true, immediate: true }
);

const filterText = ref("");
watch(filterText, val => {
  treeRef.value!.filter(val);
});

// 过滤
const filterNode = (value: string, data: { [key: string]: any }, node: any) => {
  if (!value) return true;
  let parentNode = node.parent,
    labels = [node.label],
    level = 1;
  while (level < node.level) {
    labels = [...labels, parentNode.label];
    parentNode = parentNode.parent;
    level++;
  }
  return labels.some(label => label.indexOf(value) !== -1);
};

// 切换树节点的展开或折叠状态
// const toggleTreeNodes = (isExpand: boolean) => {
//   let nodes = treeRef.value?.store.nodesMap;
//   if (!nodes) return;
//   for (const node in nodes) {
//     if (nodes.hasOwnProperty(node)) {
//       nodes[node].expanded = isExpand;
//     }
//   }
// };

// emit
const emit = defineEmits<{
  change: [value: any];
}>();

// 单选
const handleNodeClick = (data: { [key: string]: any }) => {
  // 如果是多选模式，不触发 change 事件
  if (props.multiple) {
    return;
  }

  // 单选模式，触发 change 事件
  console.log(`树形菜单选中了: ${data[props.id]}`);
  emit("change", data[props.id]);
};

// 多选
const handleCheckChange = () => {
  // 只有在多选模式下才触发 change 事件
  if (!props.multiple) {
    return;
  }

  const checkedKeys = treeRef.value?.getCheckedKeys();
  emit("change", checkedKeys);
};

const refreshData = async () => {
  if (props.requestApi) {
    const { data } = await props.requestApi();
    treeData.value = data;
    treeAllData.value = [...data];
  }
};

// 暴露给父组件使用
defineExpose({ treeData, treeAllData, treeRef, refreshData });
</script>

<style scoped lang="scss">
@import "./index";
</style>
