<template>
  <div class="guide-demo-page">
    <div class="card content-box">
      <div class="demo-header">
        <h1>🎯 用户指南系统演示</h1>
        <p class="subtitle">体验智能化的用户引导功能</p>
      </div>

      <!-- 快速开始区域 -->
      <div class="quick-start-section">
        <h2>🚀 快速开始</h2>
        <div class="start-buttons">
          <el-button 
            type="primary" 
            size="large" 
            :icon="Guide" 
            @click="startAutoGuide"
            id="quickStartBtn"
          >
            开始智能指南
          </el-button>
          <el-button 
            type="success" 
            size="large" 
            :icon="InfoFilled" 
            @click="showFeatures"
            id="featuresBtn"
          >
            查看功能特性
          </el-button>
        </div>
      </div>

      <!-- 功能演示区域 -->
      <div class="demo-sections">
        <!-- 导航演示 -->
        <div class="demo-section" id="navigationDemo">
          <h3>🧭 导航功能</h3>
          <div class="demo-content">
            <div class="nav-item" id="sidebarDemo">
              <el-icon><Menu /></el-icon>
              <span>侧边栏控制</span>
            </div>
            <div class="nav-item" id="breadcrumbDemo">
              <el-icon><Location /></el-icon>
              <span>面包屑导航</span>
            </div>
            <div class="nav-item" id="projectTreeDemo">
              <el-icon><FolderOpened /></el-icon>
              <span>项目树</span>
            </div>
          </div>
        </div>

        <!-- 拓扑功能演示 -->
        <div class="demo-section" id="topologyDemo">
          <h3>🌐 拓扑功能</h3>
          <div class="demo-content">
            <div class="topology-mockup" id="topologyCanvas">
              <div class="node" id="node1">设备1</div>
              <div class="node" id="node2">设备2</div>
              <div class="node" id="node3">设备3</div>
              <div class="connection"></div>
            </div>
            <div class="topology-controls" id="topologyControlsDemo">
              <el-button circle :icon="Refresh" title="刷新"></el-button>
              <el-button circle :icon="FullScreen" title="全屏"></el-button>
              <el-button circle :icon="ZoomIn" title="放大"></el-button>
            </div>
          </div>
        </div>

        <!-- 设备管理演示 -->
        <div class="demo-section" id="deviceDemo">
          <h3>📱 设备管理</h3>
          <div class="demo-content">
            <div class="device-list" id="deviceListDemo">
              <div class="device-item">
                <el-icon><Monitor /></el-icon>
                <span>路由器 - 192.168.1.1</span>
                <el-button size="small" type="primary">查看</el-button>
              </div>
              <div class="device-item">
                <el-icon><Connection /></el-icon>
                <span>交换机 - 192.168.1.2</span>
                <el-button size="small" type="primary">配置</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统设置演示 -->
        <div class="demo-section" id="settingsDemo">
          <h3>⚙️ 系统设置</h3>
          <div class="demo-content">
            <div class="settings-grid">
              <div class="setting-item" id="languageDemo">
                <el-icon><Globe /></el-icon>
                <span>语言切换</span>
              </div>
              <div class="setting-item" id="themeDemo">
                <el-icon><Brush /></el-icon>
                <span>主题设置</span>
              </div>
              <div class="setting-item" id="darkModeDemo">
                <el-icon><Moon /></el-icon>
                <span>暗黑模式</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 指南控制面板 -->
      <div class="guide-control-panel">
        <h3>🎮 指南控制</h3>
        <div class="control-buttons">
          <el-button @click="startProjectGuide">项目指南</el-button>
          <el-button @click="startTopologyGuide">拓扑指南</el-button>
          <el-button @click="startCustomDemo">自定义演示</el-button>
          <el-button @click="resetDemo" type="warning">重置演示</el-button>
        </div>
        
        <div class="guide-status">
          <el-tag :type="isGuideActive ? 'success' : 'info'">
            状态: {{ isGuideActive ? '指南激活中' : '指南未激活' }}
          </el-tag>
          <el-tag v-if="currentGuideType" type="primary">
            类型: {{ currentGuideType }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 浮动帮助按钮 -->
    <div class="floating-help">
      <el-tooltip content="随时获取帮助" placement="left">
        <el-button 
          type="primary" 
          circle 
          size="large"
          :icon="QuestionFilled"
          @click="startAutoGuide"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts" name="GuideDemo">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { 
  Guide, 
  InfoFilled, 
  Menu, 
  Location, 
  FolderOpened,
  Refresh,
  FullScreen,
  ZoomIn,
  Monitor,
  Connection,
  Globe,
  Brush,
  Moon,
  QuestionFilled
} from "@element-plus/icons-vue";
import { useGuide } from "@/hooks/useGuide";

const {
  isGuideActive,
  currentGuideType,
  startAutoGuide: startAutoGuideHook,
  startProjectGuide: startProjectGuideHook,
  startTopologyGuide: startTopologyGuideHook,
  startCustomGuide
} = useGuide();

// 启动智能指南
const startAutoGuide = () => {
  startAutoGuideHook({
    allowClose: true,
    animate: true,
    opacity: 0.75,
    padding: 10
  });
};

// 启动项目指南
const startProjectGuide = () => {
  startProjectGuideHook({
    allowClose: true,
    animate: true,
    opacity: 0.75
  });
};

// 启动拓扑指南
const startTopologyGuide = () => {
  startTopologyGuideHook({
    allowClose: true,
    animate: true,
    opacity: 0.75
  });
};

// 启动自定义演示
const startCustomDemo = () => {
  const demoSteps = [
    {
      element: "#quickStartBtn",
      popover: {
        title: "快速开始按钮",
        description: "点击这里可以启动智能指南，系统会自动检测页面功能并生成合适的指南步骤",
        side: "bottom"
      }
    },
    {
      element: "#navigationDemo",
      popover: {
        title: "导航功能演示",
        description: "这里展示了系统的主要导航功能，包括侧边栏、面包屑和项目树",
        side: "top"
      }
    },
    {
      element: "#topologyCanvas",
      popover: {
        title: "拓扑图演示",
        description: "这是网络拓扑图的模拟界面，显示设备之间的连接关系",
        side: "left"
      }
    },
    {
      element: "#topologyControlsDemo",
      popover: {
        title: "拓扑控制按钮",
        description: "使用这些按钮可以控制拓扑图的显示和交互",
        side: "left"
      }
    },
    {
      element: "#deviceListDemo",
      popover: {
        title: "设备列表",
        description: "这里显示了网络中的所有设备，您可以查看和配置每个设备",
        side: "top"
      }
    }
  ];

  startCustomGuide(demoSteps, {
    allowClose: true,
    animate: true,
    opacity: 0.8
  });
};

// 显示功能特性
const showFeatures = () => {
  ElMessage({
    message: "功能特性：智能检测、多语言支持、响应式设计、无缝集成",
    type: "success",
    duration: 3000
  });
};

// 重置演示
const resetDemo = () => {
  ElMessage({
    message: "演示已重置，您可以重新开始体验指南功能",
    type: "info",
    duration: 2000
  });
};
</script>

<style scoped lang="scss">
.guide-demo-page {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 2.5rem;
    color: var(--el-color-primary);
    margin-bottom: 10px;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: var(--el-text-color-regular);
  }
}

.quick-start-section {
  text-align: center;
  margin-bottom: 40px;
  
  h2 {
    margin-bottom: 20px;
    color: var(--el-text-color-primary);
  }
  
  .start-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }
}

.demo-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
  
  h3 {
    margin-bottom: 15px;
    color: var(--el-color-primary);
    border-bottom: 2px solid var(--el-color-primary-light-8);
    padding-bottom: 5px;
  }
}

.demo-content {
  .nav-item, .setting-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    background: var(--el-bg-color-page);
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--el-color-primary-light-9);
      transform: translateX(5px);
    }
  }
  
  .topology-mockup {
    position: relative;
    height: 120px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    margin-bottom: 15px;
    
    .node {
      position: absolute;
      width: 60px;
      height: 40px;
      background: var(--el-color-primary);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      
      &#node1 { top: 20px; left: 20px; }
      &#node2 { top: 20px; right: 20px; }
      &#node3 { bottom: 20px; left: 50%; transform: translateX(-50%); }
    }
    
    .connection {
      position: absolute;
      top: 40px;
      left: 80px;
      right: 80px;
      height: 2px;
      background: var(--el-color-primary-light-5);
    }
  }
  
  .device-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    margin-bottom: 10px;
    
    span {
      flex: 1;
    }
  }
  
  .settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
}

.topology-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.guide-control-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-bottom: 15px;
    color: var(--el-color-primary);
  }
  
  .control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
  }
  
  .guide-status {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
}

.floating-help {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

@media (max-width: 768px) {
  .demo-sections {
    grid-template-columns: 1fr;
  }
  
  .start-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .control-buttons {
    flex-direction: column;
  }
}
</style>
