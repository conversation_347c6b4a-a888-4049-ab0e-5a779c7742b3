<template>
  <div class="card filter">
    <h4 v-if="title" class="title sle">
      {{ title }}
    </h4>
    <div class="search">
      <el-input v-model="filterText" :placeholder="t('common.filterTip')" clearable />
      <el-dropdown v-if="$slots.dropdown" trigger="click">
        <el-icon size="20"><More /></el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <slot name="dropdown" />
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-scrollbar :style="{ height: title ? `calc(100% - 95px)` : `calc(100% - 56px)` }" style="width: 182px">
      <el-tree
        ref="treeRef"
        default-expand-all
        :node-key="id"
        :data="multiple ? treeData : treeAllData"
        :show-checkbox="multiple"
        :check-strictly="false"
        :current-node-key="!multiple ? selected : ''"
        :highlight-current="!multiple"
        :expand-on-click-node="false"
        :check-on-click-node="multiple"
        :props="defaultProps"
        :filter-node-method="filterNode"
        :default-checked-keys="multiple ? selected : []"
        @node-click="handleNodeClick"
        @check="handleCheckChange"
      >
        <template #default="scope">
          <span class="el-tree-node__label">
            <slot :row="scope">
              {{ scope.node.label }}
            </slot>
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts" name="TreeFilter">
import { ref, watch, onBeforeMount, nextTick } from "vue";
import { ElTree } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// 接收父组件参数并设置默认值
interface TreeFilterProps {
  requestApi?: (data?: any) => Promise<any>; // 请求分类数据的 api ==> 非必传
  data?: { [key: string]: any }[]; // 分类数据，如果有分类数据，则不会执行 api 请求 ==> 非必传
  title?: string; // treeFilter 标题 ==> 非必传
  id?: string; // 选择的id ==> 非必传，默认为 "id"
  label?: string; // 显示的label ==> 非必传，默认为 "label"
  multiple?: boolean; // 是否为多选 ==> 非必传，默认为 false
  defaultValue?: any; // 默认选中的值 ==> 非必传
}
const props = withDefaults(defineProps<TreeFilterProps>(), {
  id: "id",
  label: "label",
  multiple: false
});

const defaultProps = {
  children: "children",
  label: props.label
};

const treeRef = ref<InstanceType<typeof ElTree>>();
const treeData = ref<{ [key: string]: any }[]>([]);
const treeAllData = ref<{ [key: string]: any }[]>([]);

const selected = ref();
const setSelected = () => {
  if (props.multiple) selected.value = Array.isArray(props.defaultValue) ? props.defaultValue : [props.defaultValue];
  else selected.value = typeof props.defaultValue === "string" ? props.defaultValue : "";
};

onBeforeMount(async () => {
  setSelected();
  if (props.requestApi) {
    const { data } = await props.requestApi!();
    treeData.value = data;
    // 不添加"全部"选项
    treeAllData.value = [...data];

    // 如果有数据且没有默认选中值，则默认选中第一个
    if (data && data.length > 0 && !props.defaultValue) {
      const firstNode = data[0];
      if (firstNode && firstNode[props.id]) {
        // 设置当前选中节点
        nextTick(() => {
          if (treeRef.value && typeof treeRef.value.setCurrentKey === "function") {
            treeRef.value.setCurrentKey(firstNode[props.id]);
            // 触发change事件
            emit("change", firstNode[props.id]);
          }
        });
      }
    }
  }
});

// 使用 nextTick 防止打包后赋值不生效，开发环境是正常的
watch(
  () => props.defaultValue,
  () => nextTick(() => setSelected()),
  { deep: true, immediate: true }
);

watch(
  () => props.data,
  () => {
    if (props.data?.length) {
      treeData.value = props.data;
      // 不添加"全部"选项
      treeAllData.value = [...props.data];

      // 如果有数据且没有默认选中值，则默认选中第一个
      if (props.data.length > 0 && !props.defaultValue) {
        const firstNode = props.data[0];
        if (firstNode && firstNode[props.id]) {
          // 设置当前选中节点
          nextTick(() => {
            if (treeRef.value && typeof treeRef.value.setCurrentKey === "function") {
              treeRef.value.setCurrentKey(firstNode[props.id]);
              // 触发change事件
              emit("change", firstNode[props.id]);
            }
          });
        }
      }
    }
  },
  { deep: true, immediate: true }
);

const filterText = ref("");
watch(filterText, val => {
  treeRef.value!.filter(val);
});

// 过滤
const filterNode = (value: string, data: { [key: string]: any }, node: any) => {
  if (!value) return true;
  let parentNode = node.parent,
    labels = [node.label],
    level = 1;
  while (level < node.level) {
    labels = [...labels, parentNode.label];
    parentNode = parentNode.parent;
    level++;
  }
  return labels.some(label => label.indexOf(value) !== -1);
};

// 切换树节点的展开或折叠状态
// const toggleTreeNodes = (isExpand: boolean) => {
//   let nodes = treeRef.value?.store.nodesMap;
//   if (!nodes) return;
//   for (const node in nodes) {
//     if (nodes.hasOwnProperty(node)) {
//       nodes[node].expanded = isExpand;
//     }
//   }
// };

// emit
const emit = defineEmits<{
  change: [value: any];
}>();

// 单选
const handleNodeClick = (data: { [key: string]: any }) => {
  if (props.multiple) return;
  // 打印日志，查看单选时的值类型
  console.log("单选值：", data[props.id], "类型：", typeof data[props.id]);
  emit("change", data[props.id]);
};

// 多选
const handleCheckChange = () => {
  const checkedKeys = treeRef.value?.getCheckedKeys();
  // 打印日志，查看多选时的值类型
  console.log("多选值：", checkedKeys, "类型：", typeof checkedKeys, Array.isArray(checkedKeys) ? "是数组" : "不是数组");
  emit("change", checkedKeys);
};

const refreshData = async () => {
  if (props.requestApi) {
    const { data } = await props.requestApi();
    treeData.value = data;
    // 不添加"全部"选项
    treeAllData.value = [...data];

    // 如果有数据且没有默认选中值，则默认选中第一个
    if (data && data.length > 0 && !props.defaultValue) {
      const firstNode = data[0];
      if (firstNode && firstNode[props.id]) {
        // 设置当前选中节点
        nextTick(() => {
          if (treeRef.value && typeof treeRef.value.setCurrentKey === "function") {
            treeRef.value.setCurrentKey(firstNode[props.id]);
            // 触发change事件
            emit("change", firstNode[props.id]);
          }
        });
      }
    }
  }
};

// 暴露给父组件使用
defineExpose({ treeData, treeAllData, treeRef, refreshData });
</script>

<style scoped lang="scss">
@import "./index";
</style>
