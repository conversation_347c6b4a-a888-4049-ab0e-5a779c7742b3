<template>
  <div class="topology-wrapper" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 拓扑图容器 -->
    <div ref="container" class="topology-container"></div>

    <!-- 图例 -->
    <TopologyLegend />

    <!-- 悬浮按钮 -->
    <FloatingButtons
      :current-direction="currentDirection"
      @toggle-direction="toggleDirection"
      @reset-zoom="resetZoom"
      @refresh-topology="refreshTopology"
    />

    <!-- 设备信息弹窗 -->
    <DeviceInfoPopup
      :device-info="selectedDevice"
      :visible="showDeviceInfo"
      :x="popupX"
      :y="popupY"
      @close="closeDeviceInfo"
      @refresh="refreshTopology"
      @set-as-exit-device="handleSetAsExitDevice"
      @cancel-exit-device="handleCancelExitDevice"
      @view-device-details="handleViewDeviceDetails"
    />
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import DeviceInfoPopup from "./DeviceInfoPopup.vue";
import TopologyLegend from "./components/TopologyLegend.vue";
import FloatingButtons from "./components/FloatingButtons.vue";
import { useD3Topology } from "./hooks/useD3Topology";

// 使用全局 D3 对象
// 注意：这里使用的是通过 CDN 加载的全局 d3 对象
// 如果需要使用模块化导入，请取消下面的注释并注释这一行
// import * as d3 from "d3";

const { t, locale } = useI18n();
const globalStore = useGlobalStore();

const props = defineProps({
  // 布局方向
  direction: {
    type: String,
    default: "vertical", // vertical | horizontal
    validator: value => ["vertical", "horizontal"].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  }
});

const emit = defineEmits(["nodeClick", "edgeClick", "refresh", "setAsExitDevice", "cancelExitDevice", "viewDeviceDetails"]);

// 使用拓扑图核心逻辑Hook
const {
  container,
  currentDirection,
  selectedDevice,
  showDeviceInfo,
  popupX,
  popupY,
  refreshTopology,
  toggleDirection,
  resetZoom,
  closeDeviceInfo
} = useD3Topology(props, emit, { t, locale, globalStore });

// 处理“设为出口设备”按钮点击事件
const handleSetAsExitDevice = deviceInfo => {
  // 将事件和设备信息传递给父组件
  emit("setAsExitDevice", deviceInfo);
};

// 处理"取消出口设备"按钮点击事件
const handleCancelExitDevice = deviceInfo => {
  // 将事件和设备信息传递给父组件
  emit("cancelExitDevice", deviceInfo);
};

// 处理“查看详情”按钮点击事件
const handleViewDeviceDetails = deviceInfo => {
  // 将事件和设备信息传递给父组件
  emit("viewDeviceDetails", deviceInfo);
  // 切换到项目目录标签页
  // 注意：这里需要父组件提供切换标签页的方法
};
</script>

<style lang="scss" scoped>
@import "./styles/index.scss";
</style>
