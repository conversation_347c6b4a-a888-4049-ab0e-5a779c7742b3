/* 组件样式增强 */
:deep(.node) {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
:deep(.node:hover image) {
  filter: brightness(1.2);
}

/* 添加点击反馈 - 不移动节点，只改变亮度 */
:deep(.node:active) {
  filter: brightness(1.3);
}

/* 包装器样式 */
.topology-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 50px; /* 添加底部填充，为footer留出空间 */
}

.topology-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative; /* 添加相对定位，作为浮动按钮的定位参考 */
}
