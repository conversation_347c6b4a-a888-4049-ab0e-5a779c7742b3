<template>
  <div class="floating-buttons topology-controls" id="topologyControls">
    <el-button
      type="primary"
      circle
      size="large"
      @click="toggleDirection"
      :title="t('topology.' + (currentDirection === 'vertical' ? 'switchToHorizontal' : 'switchToVertical'))"
    >
      <el-icon><component :is="currentDirection === 'vertical' ? Right : Bottom" /></el-icon>
    </el-button>
    <button class="reset-button" @click="resetZoom" :title="t('topology.resetZoom')"></button>
    <el-button type="primary" circle size="large" @click="refreshTopology" :title="t('common.refresh')">
      <el-icon><Refresh /></el-icon>
    </el-button>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { Refresh, Right, Bottom } from "@element-plus/icons-vue";

const { t } = useI18n();

const { currentDirection } = defineProps({
  currentDirection: {
    type: String,
    required: true
  }
});

const emit = defineEmits(["toggleDirection", "resetZoom", "refreshTopology"]);

const toggleDirection = () => {
  emit("toggleDirection");
};

const resetZoom = () => {
  emit("resetZoom");
};

const refreshTopology = () => {
  emit("refreshTopology");
};
</script>

<style scoped>
/* 悬浮按钮样式 */
.floating-buttons {
  position: fixed; /* 使用fixed定位，确保始终可见 */
  bottom: 50px; /* 增加底部距离，避免与footer重叠 */
  right: 30px;
  z-index: 999; /* 高的z-index确保在拓扑图上层 */
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto; /* 确保可以点击 */
  align-items: center; /* 确保按钮居中对齐 */
  width: 48px; /* 固定宽度，确保所有按钮对齐 */
}

.floating-buttons .el-button {
  width: 48px; /* 增大按钮尺寸 */
  height: 48px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-color: #409eff; /* 确保背景色正确 */
  border-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0 auto; /* 水平居中 */
}

.reset-button {
  width: 48px; /* 增大按钮尺寸 */
  height: 48px;
  border-radius: 50%;
  background-color: #409eff;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

/* 使用伪元素创建图标 */
.reset-button::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.reset-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.25);
  background-color: #66b1ff;
}

.reset-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
  background-color: #3a8ee6;
}

.floating-buttons .el-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.25);
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.floating-buttons .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
  background-color: #3a8ee6;
  border-color: #3a8ee6;
}

.floating-buttons .el-icon {
  font-size: 20px; /* 增大图标尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin: 0; /* 移除可能的默认边距 */
  padding: 0; /* 移除可能的默认填充 */
  line-height: 1; /* 确保行高不影响对齐 */
}

/* 确保图标内部的SVG元素也居中 */
.floating-buttons .el-icon svg {
  display: block;
  margin: auto;
}
</style>
