import { ref, computed } from "vue";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

export interface GuideStep {
  element: string;
  popover: {
    title: string;
    description: string;
    side?: "top" | "right" | "bottom" | "left";
    align?: "start" | "center" | "end";
  };
}

export interface GuideConfig {
  allowClose?: boolean;
  animate?: boolean;
  opacity?: number;
  padding?: number;
  allowKeyboardControl?: boolean;
  disableActiveInteraction?: boolean;
}

/**
 * 用户指南 Hook
 * 提供可复用的用户指南功能
 */
export function useGuide() {
  const { t } = useI18n();
  const route = useRoute();

  const isGuideActive = ref(false);
  const currentGuideType = ref<string>("");

  // 默认配置
  const defaultConfig: GuideConfig = {
    allowClose: true,
    animate: true,
    opacity: 0.75,
    padding: 10,
    allowKeyboardControl: true,
    disableActiveInteraction: false
  };

  // 创建驱动实例
  const createDriver = (steps: GuideStep[], config: GuideConfig = {}) => {
    const finalConfig = { ...defaultConfig, ...config };

    return driver({
      ...finalConfig,
      doneBtnText: t("guide.finish"),
      nextBtnText: t("guide.nextStep"),
      prevBtnText: t("guide.prevStep"),
      steps: steps.map(step => ({
        ...step,
        popover: {
          ...step.popover,
          side: step.popover.side || "bottom",
          align: step.popover.align || "start"
        }
      })),
      onDestroyed: () => {
        isGuideActive.value = false;
        currentGuideType.value = "";
      }
    });
  };

  // 通用指南步骤定义
  const getCommonSteps = (): GuideStep[] => [
    {
      element: "#collapseIcon",
      popover: {
        title: t("guide.navigation.sidebar.title"),
        description: t("guide.navigation.sidebar.description"),
        side: "right"
      }
    },
    {
      element: "#breadcrumb",
      popover: {
        title: t("guide.navigation.breadcrumb.title"),
        description: t("guide.navigation.breadcrumb.description"),
        side: "bottom"
      }
    },
    {
      element: "#language",
      popover: {
        title: t("guide.header.language.title"),
        description: t("guide.header.language.description"),
        side: "left"
      }
    },
    {
      element: "#darkMode",
      popover: {
        title: t("guide.header.darkMode.title"),
        description: t("guide.header.darkMode.description"),
        side: "left"
      }
    },
    {
      element: "#themeSetting",
      popover: {
        title: t("guide.header.theme.title"),
        description: t("guide.header.theme.description"),
        side: "left"
      }
    },
    {
      element: "#fullscreen",
      popover: {
        title: t("guide.header.fullscreen.title"),
        description: t("guide.header.fullscreen.description"),
        side: "left"
      }
    }
  ];

  // 项目页面指南步骤
  const getProjectSteps = (): GuideStep[] => [
    {
      element: ".tree-filter",
      popover: {
        title: t("guide.navigation.projectTree.title"),
        description: t("guide.navigation.projectTree.description"),
        side: "right"
      }
    },
    {
      element: ".tabs-header",
      popover: {
        title: t("guide.tabs.topology.title"),
        description: t("guide.tabs.topology.description"),
        side: "bottom"
      }
    },
    {
      element: ".topology-tab-content",
      popover: {
        title: t("guide.topology.canvas.title"),
        description: t("guide.topology.canvas.description"),
        side: "top"
      }
    },
    ...getCommonSteps()
  ];

  // 拓扑图指南步骤
  const getTopologySteps = (): GuideStep[] => [
    {
      element: ".topology-tab-content",
      popover: {
        title: t("guide.topology.canvas.title"),
        description: t("guide.topology.canvas.description"),
        side: "top"
      }
    },
    {
      element: ".topology-controls",
      popover: {
        title: t("guide.topology.controls.title"),
        description: t("guide.topology.controls.description"),
        side: "bottom"
      }
    }
  ];

  // 设备列表指南步骤
  const getDeviceListSteps = (): GuideStep[] => [
    {
      element: ".table-box",
      popover: {
        title: t("guide.devices.list.title"),
        description: t("guide.devices.list.description"),
        side: "top"
      }
    },
    {
      element: ".table-header",
      popover: {
        title: t("guide.devices.actions.title"),
        description: t("guide.devices.actions.description"),
        side: "bottom"
      }
    }
  ];

  // 启动通用指南
  const startCommonGuide = (config?: GuideConfig) => {
    const steps = getCommonSteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "common";
    driverObj.drive();
  };

  // 启动项目页面指南
  const startProjectGuide = (config?: GuideConfig) => {
    const steps = getProjectSteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "project";
    driverObj.drive();
  };

  // 启动拓扑图指南
  const startTopologyGuide = (config?: GuideConfig) => {
    const steps = getTopologySteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "topology";
    driverObj.drive();
  };

  // 启动设备列表指南
  const startDeviceListGuide = (config?: GuideConfig) => {
    const steps = getDeviceListSteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "deviceList";
    driverObj.drive();
  };

  // 特征检测 - 检查页面元素是否存在
  const detectFeatures = () => {
    const features = {
      hasTreeFilter: !!document.querySelector(".tree-filter"),
      hasTopologyTab: !!document.querySelector(".topology-tab-content"),
      hasProjectTab: !!document.querySelector(".project-tab-content"),
      hasDeviceTable: !!document.querySelector(".table-box"),
      hasTabsHeader: !!document.querySelector(".tabs-header"),
      hasTopologyControls: !!document.querySelector("#topologyControls"),
      hasSidebar: !!document.querySelector("#collapseIcon"),
      hasBreadcrumb: !!document.querySelector("#breadcrumb"),
      hasLanguageSwitch: !!document.querySelector("#language"),
      hasThemeSettings: !!document.querySelector("#themeSetting"),
      hasDarkMode: !!document.querySelector("#darkMode"),
      hasFullscreen: !!document.querySelector("#fullscreen"),
      hasMessage: !!document.querySelector("#message"),
      hasSearchMenu: !!document.querySelector("#searchMenu")
    };

    return features;
  };

  // 动态生成指南步骤
  const generateDynamicSteps = (): GuideStep[] => {
    const features = detectFeatures();
    const steps: GuideStep[] = [];

    // 添加欢迎步骤
    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.welcome.description"),
        side: "bottom"
      }
    });

    // 根据检测到的特征添加相应步骤
    if (features.hasSidebar) {
      steps.push({
        element: "#collapseIcon",
        popover: {
          title: t("guide.navigation.sidebar.title"),
          description: t("guide.navigation.sidebar.description"),
          side: "right"
        }
      });
    }

    if (features.hasBreadcrumb) {
      steps.push({
        element: "#breadcrumb",
        popover: {
          title: t("guide.navigation.breadcrumb.title"),
          description: t("guide.navigation.breadcrumb.description"),
          side: "bottom"
        }
      });
    }

    if (features.hasTreeFilter) {
      steps.push({
        element: ".tree-filter",
        popover: {
          title: t("guide.navigation.projectTree.title"),
          description: t("guide.navigation.projectTree.description"),
          side: "right"
        }
      });
    }

    if (features.hasTabsHeader) {
      steps.push({
        element: ".tabs-header",
        popover: {
          title: t("guide.tabs.topology.title"),
          description: t("guide.tabs.topology.description"),
          side: "bottom"
        }
      });
    }

    if (features.hasTopologyTab) {
      steps.push({
        element: ".topology-tab-content",
        popover: {
          title: t("guide.topology.canvas.title"),
          description: t("guide.topology.canvas.description"),
          side: "top"
        }
      });
    }

    if (features.hasTopologyControls) {
      steps.push({
        element: "#topologyControls",
        popover: {
          title: t("guide.topology.controls.title"),
          description: t("guide.topology.controls.description"),
          side: "left"
        }
      });
    }

    if (features.hasDeviceTable) {
      steps.push({
        element: ".table-box",
        popover: {
          title: t("guide.devices.list.title"),
          description: t("guide.devices.list.description"),
          side: "top"
        }
      });
    }

    // 添加头部功能指南
    if (features.hasLanguageSwitch) {
      steps.push({
        element: "#language",
        popover: {
          title: t("guide.header.language.title"),
          description: t("guide.header.language.description"),
          side: "left"
        }
      });
    }

    if (features.hasDarkMode) {
      steps.push({
        element: "#darkMode",
        popover: {
          title: t("guide.header.darkMode.title"),
          description: t("guide.header.darkMode.description"),
          side: "left"
        }
      });
    }

    if (features.hasThemeSettings) {
      steps.push({
        element: "#themeSetting",
        popover: {
          title: t("guide.header.theme.title"),
          description: t("guide.header.theme.description"),
          side: "left"
        }
      });
    }

    if (features.hasFullscreen) {
      steps.push({
        element: "#fullscreen",
        popover: {
          title: t("guide.header.fullscreen.title"),
          description: t("guide.header.fullscreen.description"),
          side: "left"
        }
      });
    }

    // 添加指南按钮本身的介绍
    steps.push({
      element: "#guide",
      popover: {
        title: t("guide.header.guide.title"),
        description: t("guide.header.guide.description"),
        side: "left"
      }
    });

    return steps;
  };

  // 根据当前路由自动选择合适的指南
  const startAutoGuide = (config?: GuideConfig) => {
    const dynamicSteps = generateDynamicSteps();
    const driverObj = createDriver(dynamicSteps, config);
    isGuideActive.value = true;
    currentGuideType.value = "auto";
    driverObj.drive();
  };

  // 创建自定义指南
  const startCustomGuide = (steps: GuideStep[], config?: GuideConfig) => {
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "custom";
    driverObj.drive();
  };

  return {
    isGuideActive: computed(() => isGuideActive.value),
    currentGuideType: computed(() => currentGuideType.value),
    startCommonGuide,
    startProjectGuide,
    startTopologyGuide,
    startDeviceListGuide,
    startAutoGuide,
    startCustomGuide,
    createDriver,
    getCommonSteps,
    getProjectSteps,
    getTopologySteps,
    getDeviceListSteps
  };
}
