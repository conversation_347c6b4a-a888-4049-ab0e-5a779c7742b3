import { ref, computed } from "vue";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import { useI18n } from "vue-i18n";
import { printDebugInfo, checkGuideElements } from "@/utils/guideDebug";

export interface GuideStep {
  element: string;
  popover: {
    title: string;
    description: string;
    side?: "top" | "right" | "bottom" | "left";
    align?: "start" | "center" | "end";
  };
}

export interface GuideConfig {
  allowClose?: boolean;
  animate?: boolean;
  opacity?: number;
  padding?: number;
  allowKeyboardControl?: boolean;
  disableActiveInteraction?: boolean;
}

/**
 * 用户指南 Hook
 * 提供可复用的用户指南功能
 */
export function useGuide() {
  const { t } = useI18n();

  const isGuideActive = ref(false);
  const currentGuideType = ref<string>("");

  // 默认配置
  const defaultConfig: GuideConfig = {
    allowClose: true,
    animate: true,
    opacity: 0.75,
    padding: 10,
    allowKeyboardControl: true,
    disableActiveInteraction: false
  };

  // 创建驱动实例
  const createDriver = (steps: GuideStep[], config: GuideConfig = {}) => {
    const finalConfig = { ...defaultConfig, ...config };

    // 过滤掉不存在的元素
    const validSteps = steps.filter(step => {
      if (step.element === "body") return true;
      const element = document.querySelector(step.element);
      if (!element) {
        console.warn(`Guide element not found: ${step.element}`);
        return false;
      }
      return true;
    });

    console.log(`Creating guide with ${validSteps.length} valid steps out of ${steps.length} total steps`);

    return driver({
      ...finalConfig,
      doneBtnText: t("guide.finish"),
      nextBtnText: t("guide.nextStep"),
      prevBtnText: t("guide.prevStep"),
      steps: validSteps.map(step => ({
        ...step,
        popover: {
          ...step.popover,
          side: step.popover.side || "bottom",
          align: step.popover.align || "start"
        }
      })),
      onDestroyed: () => {
        isGuideActive.value = false;
        currentGuideType.value = "";
      },
      onHighlightStarted: element => {
        console.log("Highlighting element:", element);
      },
      onDeselected: element => {
        console.log("Deselected element:", element);
      }
    });
  };

  // 通用指南步骤定义
  const getCommonSteps = (): GuideStep[] => [
    {
      element: "#collapseIcon",
      popover: {
        title: t("guide.navigation.sidebar.title"),
        description: t("guide.navigation.sidebar.description"),
        side: "right"
      }
    },
    {
      element: "#breadcrumb",
      popover: {
        title: t("guide.navigation.breadcrumb.title"),
        description: t("guide.navigation.breadcrumb.description"),
        side: "bottom"
      }
    },
    {
      element: "#language",
      popover: {
        title: t("guide.header.language.title"),
        description: t("guide.header.language.description"),
        side: "left"
      }
    },
    {
      element: "#darkMode",
      popover: {
        title: t("guide.header.darkMode.title"),
        description: t("guide.header.darkMode.description"),
        side: "left"
      }
    },
    {
      element: "#themeSetting",
      popover: {
        title: t("guide.header.theme.title"),
        description: t("guide.header.theme.description"),
        side: "left"
      }
    },
    {
      element: "#fullscreen",
      popover: {
        title: t("guide.header.fullscreen.title"),
        description: t("guide.header.fullscreen.description"),
        side: "left"
      }
    }
  ];

  // 根据当前页面路径获取对应的指南步骤
  const getPageSpecificSteps = (): GuideStep[] => {
    const currentPath = window.location.pathname;

    if (currentPath.includes('/project')) {
      return getProjectPageSteps();
    } else if (currentPath.includes('/rnet')) {
      return getRnetPageSteps();
    } else if (currentPath.includes('/terminal')) {
      return getTerminalPageSteps();
    } else if (currentPath.includes('/home')) {
      return getHomePageSteps();
    } else {
      return getCommonSteps();
    }
  };

  // 项目页面指南步骤
  const getProjectPageSteps = (): GuideStep[] => {
    const steps: GuideStep[] = [];

    // 1. 欢迎步骤
    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.project.welcome.description"),
        side: "bottom",
        align: "center"
      }
    });

    // 2. 左侧项目选择器
    if (document.querySelector(".tree-filter")) {
      steps.push({
        element: ".tree-filter",
        popover: {
          title: t("guide.project.selector.title"),
          description: t("guide.project.selector.description"),
          side: "right",
          align: "start"
        }
      });
    }

    // 3. 标签页切换
    if (document.querySelector(".tabs-header")) {
      steps.push({
        element: ".tabs-header",
        popover: {
          title: t("guide.project.tabs.title"),
          description: t("guide.project.tabs.description"),
          side: "bottom",
          align: "start"
        }
      });
    }

    // 4. 拓扑图内容
    if (document.querySelector(".topology-tab-content")) {
      steps.push({
        element: ".topology-tab-content",
        popover: {
          title: t("guide.project.topology.title"),
          description: t("guide.project.topology.description"),
          side: "top",
          align: "center"
        }
      });
    }

    // 5. 设备卡片区域
    if (document.querySelector(".card-space")) {
      steps.push({
        element: ".card-space",
        popover: {
          title: t("guide.project.deviceCards.title"),
          description: t("guide.project.deviceCards.description"),
          side: "top",
          align: "center"
        }
      });
    }

    // 6. 设备表格
    if (document.querySelector(".project-tab-content .el-table")) {
      steps.push({
        element: ".project-tab-content .el-table",
        popover: {
          title: t("guide.project.deviceTable.title"),
          description: t("guide.project.deviceTable.description"),
          side: "top",
          align: "center"
        }
      });
    }

    return steps;
  };

  // 远程网络页面指南步骤
  const getRnetPageSteps = (): GuideStep[] => {
    const steps: GuideStep[] = [];

    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.rnet.welcome.description"),
        side: "bottom",
        align: "center"
      }
    });

    // 网络拓扑图
    if (document.querySelector(".card.content-box")) {
      steps.push({
        element: ".card.content-box",
        popover: {
          title: t("guide.rnet.topology.title"),
          description: t("guide.rnet.topology.description"),
          side: "top",
          align: "center"
        }
      });
    }

    // 设备列表
    if (document.querySelector(".device-list-header")) {
      steps.push({
        element: ".device-list-header",
        popover: {
          title: t("guide.rnet.deviceList.title"),
          description: t("guide.rnet.deviceList.description"),
          side: "left",
          align: "start"
        }
      });
    }

    return steps;
  };

  // 终端管理页面指南步骤
  const getTerminalPageSteps = (): GuideStep[] => {
    const steps: GuideStep[] = [];

    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.terminal.welcome.description"),
        side: "bottom",
        align: "center"
      }
    });

    // 项目选择器
    if (document.querySelector(".tree-filter")) {
      steps.push({
        element: ".tree-filter",
        popover: {
          title: t("guide.terminal.projectSelector.title"),
          description: t("guide.terminal.projectSelector.description"),
          side: "right",
          align: "start"
        }
      });
    }

    // 设备表格
    if (document.querySelector(".table-box")) {
      steps.push({
        element: ".table-box",
        popover: {
          title: t("guide.terminal.deviceTable.title"),
          description: t("guide.terminal.deviceTable.description"),
          side: "top",
          align: "center"
        }
      });
    }

    return steps;
  };

  // 首页指南步骤
  const getHomePageSteps = (): GuideStep[] => {
    const steps: GuideStep[] = [];

    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.home.welcome.description"),
        side: "bottom",
        align: "center"
      }
    });

    return steps;
  };

  // 拓扑图指南步骤
  const getTopologySteps = (): GuideStep[] => [
    {
      element: ".topology-tab-content",
      popover: {
        title: t("guide.topology.canvas.title"),
        description: t("guide.topology.canvas.description"),
        side: "top"
      }
    },
    {
      element: ".topology-controls",
      popover: {
        title: t("guide.topology.controls.title"),
        description: t("guide.topology.controls.description"),
        side: "bottom"
      }
    }
  ];

  // 设备列表指南步骤
  const getDeviceListSteps = (): GuideStep[] => [
    {
      element: ".table-box",
      popover: {
        title: t("guide.devices.list.title"),
        description: t("guide.devices.list.description"),
        side: "top"
      }
    },
    {
      element: ".table-header",
      popover: {
        title: t("guide.devices.actions.title"),
        description: t("guide.devices.actions.description"),
        side: "bottom"
      }
    }
  ];

  // 启动通用指南
  const startCommonGuide = (config?: GuideConfig) => {
    const steps = getCommonSteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "common";
    driverObj.drive();
  };

  // 启动项目页面指南
  const startProjectGuide = async (config?: GuideConfig) => {
    console.log("Starting project guide...");

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 800));

    const steps = getProjectSteps();

    if (steps.length === 0) {
      console.warn("No valid project guide steps found");
      return;
    }

    console.log(`Generated ${steps.length} project guide steps`);

    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "project";

    try {
      driverObj.drive();
    } catch (error) {
      console.error("Error starting project guide:", error);
      isGuideActive.value = false;
      currentGuideType.value = "";
    }
  };

  // 启动拓扑图指南
  const startTopologyGuide = (config?: GuideConfig) => {
    const steps = getTopologySteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "topology";
    driverObj.drive();
  };

  // 启动设备列表指南
  const startDeviceListGuide = (config?: GuideConfig) => {
    const steps = getDeviceListSteps();
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "deviceList";
    driverObj.drive();
  };

  // 特征检测 - 检查页面元素是否存在
  const detectFeatures = () => {
    const features = {
      hasTreeFilter: !!document.querySelector(".tree-filter"),
      hasTopologyTab: !!document.querySelector(".topology-tab-content"),
      hasProjectTab: !!document.querySelector(".project-tab-content"),
      hasDeviceTable: !!document.querySelector(".table-box"),
      hasTabsHeader: !!document.querySelector(".tabs-header"),
      hasTopologyControls: !!document.querySelector("#topologyControls"),
      hasSidebar: !!document.querySelector("#collapseIcon"),
      hasBreadcrumb: !!document.querySelector("#breadcrumb"),
      hasLanguageSwitch: !!document.querySelector("#language"),
      hasThemeSettings: !!document.querySelector("#themeSetting"),
      hasDarkMode: !!document.querySelector("#darkMode"),
      hasFullscreen: !!document.querySelector("#fullscreen"),
      hasMessage: !!document.querySelector("#message"),
      hasSearchMenu: !!document.querySelector("#searchMenu")
    };

    return features;
  };

  // 动态生成指南步骤
  const generateDynamicSteps = (): GuideStep[] => {
    const features = detectFeatures();
    const steps: GuideStep[] = [];

    // 添加欢迎步骤
    steps.push({
      element: "body",
      popover: {
        title: t("guide.welcome.title"),
        description: t("guide.welcome.description"),
        side: "bottom"
      }
    });

    // 根据检测到的特征添加相应步骤
    if (features.hasSidebar) {
      steps.push({
        element: "#collapseIcon",
        popover: {
          title: t("guide.navigation.sidebar.title"),
          description: t("guide.navigation.sidebar.description"),
          side: "right"
        }
      });
    }

    if (features.hasBreadcrumb) {
      steps.push({
        element: "#breadcrumb",
        popover: {
          title: t("guide.navigation.breadcrumb.title"),
          description: t("guide.navigation.breadcrumb.description"),
          side: "bottom"
        }
      });
    }

    if (features.hasTreeFilter) {
      steps.push({
        element: ".tree-filter",
        popover: {
          title: t("guide.navigation.projectTree.title"),
          description: t("guide.navigation.projectTree.description"),
          side: "right"
        }
      });
    }

    if (features.hasTabsHeader) {
      steps.push({
        element: ".tabs-header",
        popover: {
          title: t("guide.tabs.topology.title"),
          description: t("guide.tabs.topology.description"),
          side: "bottom"
        }
      });
    }

    if (features.hasTopologyTab) {
      steps.push({
        element: ".topology-tab-content",
        popover: {
          title: t("guide.topology.canvas.title"),
          description: t("guide.topology.canvas.description"),
          side: "top"
        }
      });
    }

    if (features.hasTopologyControls) {
      steps.push({
        element: "#topologyControls",
        popover: {
          title: t("guide.topology.controls.title"),
          description: t("guide.topology.controls.description"),
          side: "left"
        }
      });
    }

    if (features.hasDeviceTable) {
      steps.push({
        element: ".table-box",
        popover: {
          title: t("guide.devices.list.title"),
          description: t("guide.devices.list.description"),
          side: "top"
        }
      });
    }

    // 添加头部功能指南
    if (features.hasLanguageSwitch) {
      steps.push({
        element: "#language",
        popover: {
          title: t("guide.header.language.title"),
          description: t("guide.header.language.description"),
          side: "left"
        }
      });
    }

    if (features.hasDarkMode) {
      steps.push({
        element: "#darkMode",
        popover: {
          title: t("guide.header.darkMode.title"),
          description: t("guide.header.darkMode.description"),
          side: "left"
        }
      });
    }

    if (features.hasThemeSettings) {
      steps.push({
        element: "#themeSetting",
        popover: {
          title: t("guide.header.theme.title"),
          description: t("guide.header.theme.description"),
          side: "left"
        }
      });
    }

    if (features.hasFullscreen) {
      steps.push({
        element: "#fullscreen",
        popover: {
          title: t("guide.header.fullscreen.title"),
          description: t("guide.header.fullscreen.description"),
          side: "left"
        }
      });
    }

    // 添加指南按钮本身的介绍
    steps.push({
      element: "#guide",
      popover: {
        title: t("guide.header.guide.title"),
        description: t("guide.header.guide.description"),
        side: "left"
      }
    });

    return steps;
  };

  // 根据当前路由自动选择合适的指南
  const startAutoGuide = async (config?: GuideConfig) => {
    console.log("Starting auto guide...");

    // 在开发环境下打印调试信息
    if (process.env.NODE_ENV === "development") {
      printDebugInfo();
    }

    // 等待一小段时间确保DOM完全加载
    await new Promise(resolve => setTimeout(resolve, 500));

    const dynamicSteps = generateDynamicSteps();

    if (dynamicSteps.length === 0) {
      console.warn("No valid guide steps found");

      // 在开发环境下提供更多调试信息
      if (process.env.NODE_ENV === "development") {
        console.log("Available elements:", checkGuideElements());
      }
      return;
    }

    console.log(`Generated ${dynamicSteps.length} guide steps`);

    const driverObj = createDriver(dynamicSteps, config);
    isGuideActive.value = true;
    currentGuideType.value = "auto";

    try {
      driverObj.drive();
    } catch (error) {
      console.error("Error starting guide:", error);
      isGuideActive.value = false;
      currentGuideType.value = "";
    }
  };

  // 创建自定义指南
  const startCustomGuide = (steps: GuideStep[], config?: GuideConfig) => {
    const driverObj = createDriver(steps, config);
    isGuideActive.value = true;
    currentGuideType.value = "custom";
    driverObj.drive();
  };

  return {
    isGuideActive: computed(() => isGuideActive.value),
    currentGuideType: computed(() => currentGuideType.value),
    startCommonGuide,
    startProjectGuide,
    startTopologyGuide,
    startDeviceListGuide,
    startAutoGuide,
    startCustomGuide,
    createDriver,
    getCommonSteps,
    getProjectSteps,
    getTopologySteps,
    getDeviceListSteps
  };
}
