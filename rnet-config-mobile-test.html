<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>异地组网配置弹窗 - 移动端布局优化</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          "Helvetica Neue", Helvetica, Arial, sans-serif;
        background: #f5f5f5;
      }
      .demo-container {
        max-width: 400px;
        padding: 20px;
        margin: 0 auto;
      }
      .demo-section {
        margin-bottom: 30px;
        overflow: hidden;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
      }
      .demo-title {
        padding: 15px 20px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: white;
        background: #409eff;
      }
      .demo-content {
        padding: 20px;
      }

      /* 当前问题展示样式 */
      .current-issues {
        /* 模拟当前可能存在的问题 */
      }
      .current-issues .el-form-item__label {
        width: 220px !important; /* 固定宽度在移动端会有问题 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .current-issues .el-switch__label {
        font-size: 14px;
        white-space: nowrap;
      }
      .current-issues .ip-input-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .current-issues .ip-input-row .el-input {
        flex: 1;
        margin-right: 10px;
      }

      /* 优化后的样式 */
      .optimized {
        /* 针对移动端的优化样式 */
      }

      @media screen and (width <= 768px) {
        .optimized .el-form-item {
          margin-bottom: 28px;
        }
        .optimized .el-form-item__label {
          display: block !important;
          width: 100% !important;
          padding-right: 0 !important;
          padding-bottom: 12px;
          font-size: 15px;
          font-weight: 600;
          line-height: 1.4;
          color: #303133;
          text-align: left;
          word-break: break-word;
          white-space: normal;
        }
        .optimized .el-form-item__content {
          width: 100% !important;
          margin-left: 0 !important;
        }

        /* 开关组件优化 */
        .optimized .el-switch {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          margin: 8px 0;
        }
        .optimized .el-switch__core {
          margin-right: 12px;
        }
        .optimized .el-switch__label {
          margin: 0 6px;
          font-size: 14px !important;
          color: #606266;
          white-space: nowrap;
        }
        .optimized .el-switch__label--left {
          order: 1;
          margin-right: 12px;
          margin-left: 0;
        }
        .optimized .el-switch__label--right {
          order: 3;
          margin-right: 0;
          margin-left: 12px;
        }
        .optimized .el-switch__core {
          order: 2;
        }

        /* IP输入行优化 */
        .optimized .ip-input-row {
          display: flex;
          gap: 10px;
          align-items: flex-start;
          margin-bottom: 15px;
        }
        .optimized .ip-input-row .el-form-item {
          flex: 1;
          min-width: 0;
          margin-bottom: 0;
        }
        .optimized .ip-input-row .el-form-item__content {
          margin-left: 0 !important;
        }
        .optimized .ip-input-row .el-input {
          margin-bottom: 0;
        }
        .optimized .ip-input-row .el-input__inner {
          height: 42px;
          font-size: 14px;
          border-radius: 8px;
        }
        .optimized .ip-input-row .el-button.is-circle {
          flex-shrink: 0;
          width: 42px;
          height: 42px;
          margin-top: 0;
        }

        /* 添加按钮优化 */
        .optimized .add-ip-btn {
          margin-top: 10px;
        }

        /* 按钮组优化 */
        .optimized .form-buttons {
          display: flex;
          gap: 12px;
          justify-content: center;
          padding-top: 20px;
          margin-top: 25px;
          border-top: 1px solid #ebeef5;
        }
        .optimized .form-buttons .el-button {
          flex: 1;
          max-width: 120px;
          height: 44px;
          font-size: 15px;
          font-weight: 600;
          border-radius: 8px;
        }
      }

      @media screen and (width <= 480px) {
        .optimized .el-form-item__label {
          padding-bottom: 10px;
          font-size: 14px;
        }
        .optimized .el-switch__label {
          font-size: 13px !important;
        }
        .optimized .ip-input-row .el-input__inner {
          height: 38px;
          font-size: 13px;
        }
        .optimized .ip-input-row .el-button.is-circle {
          width: 38px;
          height: 38px;
        }
        .optimized .form-buttons {
          flex-direction: column;
          gap: 10px;
        }
        .optimized .form-buttons .el-button {
          width: 100%;
          max-width: none;
        }
      }

      /* 错误提示优化 */
      .optimized .el-form-item__error {
        margin-top: 5px;
        font-size: 12px;
        line-height: 1.4;
      }

      /* 弹窗样式 */
      .config-dialog .el-dialog {
        width: 95vw;
        max-width: 500px;
        max-height: 90vh;
        margin: 5vh auto;
        border-radius: 12px;
      }
      .config-dialog .el-dialog__header {
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;
      }
      .config-dialog .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      .config-dialog .el-dialog__body {
        max-height: 70vh;
        padding: 20px;
        overflow-y: auto;
      }

      /* 响应式优化 */
      @media screen and (width <= 768px) {
        .config-dialog .el-dialog {
          width: 96vw;
          max-height: 95vh;
          margin: 2.5vh auto;
        }
        .config-dialog .el-dialog__header {
          padding: 12px 16px;
        }
        .config-dialog .el-dialog__title {
          font-size: 16px;
        }
        .config-dialog .el-dialog__body {
          max-height: 75vh;
          padding: 16px;
        }
      }

      @media screen and (width <= 480px) {
        .config-dialog .el-dialog {
          width: 98vw;
          max-height: 98vh;
          margin: 1vh auto;
          border-radius: 8px;
        }
        .config-dialog .el-dialog__header {
          padding: 10px 12px;
        }
        .config-dialog .el-dialog__title {
          font-size: 15px;
        }
        .config-dialog .el-dialog__body {
          max-height: 80vh;
          padding: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="demo-container">
        <!-- 当前问题展示 -->
        <div class="demo-section">
          <h3 class="demo-title">当前问题展示</h3>
          <div class="demo-content">
            <div class="current-issues">
              <el-form label-width="220px">
                <el-form-item label="网段生成模式">
                  <el-switch
                    v-model="currentForm.custom"
                    :active-value="0"
                    active-text="自动生成"
                    :inactive-value="1"
                    inactive-text="自定义"
                  />
                </el-form-item>
                <el-form-item label="允许访问IP段">
                  <div v-for="(ip, index) in currentForm.allowedIPs" :key="index" class="ip-input-row">
                    <el-input v-model="currentForm.allowedIPs[index]" placeholder="请输入IP地址段" />
                    <el-button type="danger" circle v-if="currentForm.custom === 1">
                      <el-icon><Minus /></el-icon>
                    </el-button>
                  </div>
                  <el-button type="primary" circle v-if="currentForm.custom === 1">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary">确定</el-button>
                  <el-button>取消</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 优化后效果 -->
        <div class="demo-section">
          <h3 class="demo-title">优化后效果</h3>
          <div class="demo-content">
            <div class="optimized">
              <el-form>
                <el-form-item label="网段生成模式">
                  <el-switch
                    v-model="optimizedForm.custom"
                    :active-value="0"
                    active-text="自动生成"
                    :inactive-value="1"
                    inactive-text="自定义"
                  />
                </el-form-item>
                <el-form-item label="允许访问IP段">
                  <div v-for="(ip, index) in optimizedForm.allowedIPs" :key="index" class="ip-input-row">
                    <el-form-item style="flex: 1; margin-bottom: 0">
                      <el-input v-model="optimizedForm.allowedIPs[index]" placeholder="请输入IP地址段，如 ***********/24" />
                    </el-form-item>
                    <el-button type="danger" circle v-if="optimizedForm.custom === 1">
                      <el-icon><Minus /></el-icon>
                    </el-button>
                  </div>
                  <el-button type="primary" circle v-if="optimizedForm.custom === 1" class="add-ip-btn">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                </el-form-item>
                <div class="form-buttons">
                  <el-button type="primary">确定</el-button>
                  <el-button>取消</el-button>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 弹窗演示 -->
        <div class="demo-section">
          <h3 class="demo-title">弹窗演示</h3>
          <div class="demo-content">
            <el-button type="primary" @click="showDialog = true">打开配置弹窗</el-button>
          </div>
        </div>
      </div>

      <!-- 配置弹窗 -->
      <el-dialog v-model="showDialog" class="config-dialog" title="设备配置" :show-close="true">
        <div class="optimized">
          <el-form>
            <el-form-item label="网段生成模式">
              <el-switch
                v-model="dialogForm.custom"
                :active-value="0"
                active-text="自动生成"
                :inactive-value="1"
                inactive-text="自定义"
              />
            </el-form-item>
            <el-form-item label="允许访问IP段">
              <div v-for="(ip, index) in dialogForm.allowedIPs" :key="index" class="ip-input-row">
                <el-form-item style="flex: 1; margin-bottom: 0">
                  <el-input v-model="dialogForm.allowedIPs[index]" placeholder="请输入IP地址段，如 ***********/24" />
                </el-form-item>
                <el-button type="danger" circle v-if="dialogForm.custom === 1">
                  <el-icon><Minus /></el-icon>
                </el-button>
              </div>
              <el-button type="primary" circle v-if="dialogForm.custom === 1" class="add-ip-btn">
                <el-icon><Plus /></el-icon>
              </el-button>
            </el-form-item>
            <div class="form-buttons">
              <el-button type="primary" @click="showDialog = false">确定</el-button>
              <el-button @click="showDialog = false">取消</el-button>
            </div>
          </el-form>
        </div>
      </el-dialog>
    </div>

    <script>
      const { createApp, ref } = Vue;
      const { ElMessage } = ElementPlus;

      createApp({
        setup() {
          const showDialog = ref(false);

          const currentForm = ref({
            custom: 0,
            allowedIPs: ["***********/24", "10.0.0.0/8"]
          });

          const optimizedForm = ref({
            custom: 1,
            allowedIPs: ["***********/24", "10.0.0.0/8"]
          });

          const dialogForm = ref({
            custom: 1,
            allowedIPs: ["***********/24"]
          });

          return {
            showDialog,
            currentForm,
            optimizedForm,
            dialogForm
          };
        }
      })
        .use(ElementPlus)
        .mount("#app");
    </script>
  </body>
</html>
