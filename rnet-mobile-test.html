<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>异地组网移动端测试</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        padding: 10px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: #f5f7fa;
      }
      .main-box {
        display: flex;
        gap: 20px;
        align-items: flex-start;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgb(0 0 0 / 5%);
      }
      .filter {
        flex-shrink: 0;
        width: 220px;
        min-height: 160px;
        padding: 18px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
      }
      .filter .title {
        margin: 0 0 15px;
        font-size: 18px;
        font-weight: bold;
        color: #606266;
        text-align: center;
      }
      .main-content {
        flex: 1;
        overflow: auto;
      }
      .box-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
      }
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;
      }
      .card-header span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      .card-body {
        padding: 24px;
      }
      .container {
        display: flex;
        height: 650px;
      }
      .aside {
        width: 70%;
        margin-right: 20px;
      }
      .content-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 24px;
        color: #909399;
        background: #f8f9fa;
        border-radius: 16px;
      }
      .main {
        display: flex;
        flex: 1;
        flex-direction: column;
      }
      .device-list-header {
        display: flex;
        align-items: center;
        height: 50px;
        padding: 0 16px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 8px;
      }
      .device-list-container {
        flex: 1;
        overflow-y: auto;
      }
      .device-card {
        padding: 16px;
        margin-bottom: 12px;
        background: #ffffff;
        border: 1px solid #ebeef5;
        border-radius: 12px;
      }
      .card-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 12px;
        margin-bottom: 12px;
        border-bottom: 1px solid #ebeef5;
      }
      .device-info {
        margin-bottom: 8px;
        font-size: 14px;
        color: #606266;
      }
      .online-text {
        color: #67c23a;
      }
      .offline-text {
        color: #f56c6c;
      }
      .btn {
        padding: 8px 16px;
        margin-left: 8px;
        color: white;
        cursor: pointer;
        background: #409eff;
        border: none;
        border-radius: 8px;
      }

      /* 移动端适配 */
      @media screen and (width <= 768px) {
        .main-box {
          flex-direction: column;
          gap: 12px;
          padding: 12px;
        }
        .filter {
          width: 100%;
          padding: 12px;
          margin-bottom: 12px;
        }
        .filter .title {
          margin-bottom: 10px;
          font-size: 16px;
        }
        .card-header {
          flex-direction: column;
          gap: 8px;
          padding: 12px 16px;
        }
        .card-header span {
          font-size: 14px;
        }
        .card-body {
          padding: 12px;
        }
        .container {
          flex-direction: column;
          height: auto;
        }
        .aside {
          width: 100%;
          margin-right: 0;
          margin-bottom: 12px;
        }
        .content-box {
          height: 300px;
          padding: 12px;
        }
        .device-list-header {
          height: 40px;
          padding: 0 12px;
          margin-bottom: 8px;
        }
        .device-list-container {
          max-height: 300px;
        }
        .device-card {
          padding: 12px;
          margin-bottom: 8px;
        }
        .card-content {
          padding-bottom: 8px;
          margin-bottom: 8px;
        }
        .device-info {
          margin-bottom: 6px;
          font-size: 12px;
        }
        .btn {
          width: 100%;
          margin-top: 4px;
          margin-left: 0;
        }
      }

      @media screen and (width <= 480px) {
        .main-box {
          gap: 8px;
          padding: 8px;
        }
        .filter {
          padding: 8px;
        }
        .filter .title {
          font-size: 14px;
        }
        .card-header {
          padding: 8px 12px;
        }
        .card-body {
          padding: 8px;
        }
        .content-box {
          height: 250px;
          padding: 8px;
        }
        .device-list-header {
          height: 35px;
          padding: 0 8px;
        }
        .device-list-container {
          max-height: 250px;
        }
        .device-card {
          padding: 8px;
        }
        .device-info {
          font-size: 11px;
        }
      }

      @media screen and (width <= 350px) {
        .main-box {
          padding: 6px;
        }
        .filter {
          padding: 6px;
        }
        .filter .title {
          font-size: 13px;
        }
        .content-box {
          height: 200px;
        }
        .device-list-container {
          max-height: 200px;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-box">
      <!-- 左侧树形筛选器 -->
      <div class="filter">
        <h4 class="title">异地组网</h4>
        <div style="margin-bottom: 15px">
          <input
            type="text"
            placeholder="筛选提示"
            style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px"
          />
        </div>
        <div style="height: 200px; overflow-y: auto">
          <div
            style="
              padding: 8px;
              margin-bottom: 4px;
              color: white;
              cursor: pointer;
              background: rgb(64 158 255 / 80%);
              border-radius: 5px;
            "
          >
            网络组1
          </div>
          <div style="padding: 8px; margin-bottom: 4px; cursor: pointer">网络组2</div>
          <div style="padding: 8px; margin-bottom: 4px; cursor: pointer">网络组3</div>
        </div>
      </div>

      <!-- 右侧主内容 -->
      <div class="main-content">
        <div class="box-card">
          <div class="card-header">
            <span>异地组网</span>
            <div>
              <button class="btn">刷新</button>
              <button class="btn">添加</button>
            </div>
          </div>
          <div class="card-body">
            <div class="container">
              <!-- 网络拓扑图 -->
              <div class="aside">
                <div class="content-box">
                  D3网络拓扑图区域<br />
                  (300px高度)
                </div>
              </div>

              <!-- 设备列表 -->
              <div class="main">
                <div class="device-list-header">
                  <span style="font-weight: bold">异地组网设备</span>
                </div>
                <div class="device-list-container">
                  <!-- 设备卡片1 -->
                  <div class="device-card">
                    <div class="card-content">
                      <strong>设备001 (主设备)</strong>
                      <span style="cursor: pointer">⋮</span>
                    </div>
                    <div class="device-info">
                      <span class="online-text">• 在线</span>
                    </div>
                    <div class="device-info">路由器</div>
                    <div class="device-info"><strong>网络类型:</strong> CODE</div>
                    <div class="device-info"><strong>映射IP:</strong> *********** -> ********</div>
                    <div class="device-info"><strong>允许访问IP段:</strong> ***********/24</div>
                  </div>

                  <!-- 设备卡片2 -->
                  <div class="device-card">
                    <div class="card-content">
                      <strong>设备002 (从设备)</strong>
                      <span style="cursor: pointer">⋮</span>
                    </div>
                    <div class="device-info">
                      <span class="offline-text">• 离线</span>
                    </div>
                    <div class="device-info">交换机</div>
                    <div class="device-info"><strong>网络类型:</strong> SYMMETRIC</div>
                    <div class="device-info"><strong>映射IP:</strong> *********** -> ********</div>
                    <div class="device-info"><strong>允许访问IP段:</strong> ***********/24</div>
                  </div>

                  <!-- 设备卡片3 -->
                  <div class="device-card">
                    <div class="card-content">
                      <strong>设备003</strong>
                      <span style="cursor: pointer">⋮</span>
                    </div>
                    <div class="device-info">
                      <span class="online-text">• 在线</span>
                    </div>
                    <div class="device-info">无线AP</div>
                    <div class="device-info"><strong>网络类型:</strong> CODE</div>
                    <div class="device-info"><strong>映射IP:</strong> *********** -> ********</div>
                    <div class="device-info"><strong>允许访问IP段:</strong> ***********/24</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 简单的响应式测试
      function updateViewport() {
        const width = window.innerWidth;
        const info =
          document.getElementById("viewport-info") ||
          (() => {
            const div = document.createElement("div");
            div.id = "viewport-info";
            div.style.cssText =
              "position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px; z-index: 1000;";
            document.body.appendChild(div);
            return div;
          })();

        let breakpoint = "";
        if (width <= 350) breakpoint = "超小屏";
        else if (width <= 480) breakpoint = "小屏";
        else if (width <= 768) breakpoint = "中屏";
        else breakpoint = "大屏";

        info.textContent = `${width}px (${breakpoint})`;
      }

      window.addEventListener("resize", updateViewport);
      updateViewport();
    </script>
  </body>
</html>
