# 用户指南系统文档

## 概述

本项目实现了一个完整的用户指南系统，帮助新用户快速了解和掌握网络管理系统的各项功能。该系统基于 `driver.js` 构建，提供了智能特征检测、动态步骤生成、多语言支持等高级功能。

## 功能特性

### 🎯 核心功能
- **智能特征检测**: 自动检测页面可用功能，动态生成指南步骤
- **多种指南模式**: 通用指南、项目指南、拓扑指南、自定义指南
- **响应式设计**: 支持桌面端和移动端的不同显示方式
- **多语言支持**: 完整的中英文国际化支持
- **渐进式披露**: 根据用户当前页面智能选择合适的指南内容

### 🚀 高级特性
- **手动触发**: 用户可随时通过头部按钮启动指南
- **动态步骤**: 根据页面实际内容动态调整指南步骤
- **无缝集成**: 与现有UI组件完美融合，不影响正常使用
- **可配置性**: 支持自定义指南配置和样式

## 使用方法

### 1. 基础使用

#### 在组件中使用指南Hook
```vue
<script setup>
import { useGuide } from "@/hooks/useGuide";

const { 
  startAutoGuide, 
  startProjectGuide, 
  startTopologyGuide,
  isGuideActive,
  currentGuideType 
} = useGuide();

// 启动智能指南（推荐）
const handleStartGuide = () => {
  startAutoGuide({
    allowClose: true,
    animate: true,
    opacity: 0.75
  });
};
</script>
```

#### 头部指南按钮
系统已在头部工具栏集成指南按钮，用户可随时点击启动指南：
- 桌面端：显示在头部工具栏
- 移动端：集成在下拉菜单中

### 2. 指南类型

#### 智能指南 (推荐)
```javascript
startAutoGuide(config);
```
- 自动检测页面特征
- 动态生成最适合的指南步骤
- 适用于所有页面

#### 项目指南
```javascript
startProjectGuide(config);
```
- 专门针对项目管理页面
- 包含项目选择、设备管理等功能介绍

#### 拓扑指南
```javascript
startTopologyGuide(config);
```
- 专门针对网络拓扑图
- 包含拓扑操作、设备交互等功能介绍

#### 自定义指南
```javascript
const customSteps = [
  {
    element: "#myElement",
    popover: {
      title: "自定义标题",
      description: "自定义描述",
      side: "bottom"
    }
  }
];

startCustomGuide(customSteps, config);
```

### 3. 配置选项

```typescript
interface GuideConfig {
  allowClose?: boolean;        // 是否允许关闭 (默认: true)
  animate?: boolean;           // 是否启用动画 (默认: true)
  opacity?: number;            // 遮罩透明度 (默认: 0.75)
  padding?: number;            // 高亮区域内边距 (默认: 10)
  allowKeyboardControl?: boolean; // 是否支持键盘控制 (默认: true)
  disableActiveInteraction?: boolean; // 是否禁用交互 (默认: false)
}
```

## 国际化支持

### 语言文件结构
```
src/languages/modules/
├── zh.ts    # 中文语言包
└── en.ts    # 英文语言包
```

### 添加新的指南文本
在语言文件中的 `guide` 对象下添加新的键值对：

```typescript
// zh.ts
guide: {
  myNewFeature: {
    title: "新功能标题",
    description: "新功能描述"
  }
}

// en.ts
guide: {
  myNewFeature: {
    title: "New Feature Title",
    description: "New feature description"
  }
}
```

## 页面集成

### 为页面元素添加指南支持

1. **添加ID或类名**
```vue
<template>
  <div id="myFeature" class="feature-container">
    <!-- 功能内容 -->
  </div>
</template>
```

2. **在指南步骤中引用**
```javascript
const steps = [
  {
    element: "#myFeature",
    popover: {
      title: t("guide.myFeature.title"),
      description: t("guide.myFeature.description"),
      side: "bottom"
    }
  }
];
```

## 技术栈

- **driver.js**: 核心指南库
- **Vue 3**: 组件框架
- **TypeScript**: 类型支持
- **Element Plus**: UI组件库
- **Vue I18n**: 国际化支持

## 文件结构

```
src/
├── hooks/
│   └── useGuide.ts                    # 指南系统核心Hook
├── layouts/components/Header/components/
│   └── Guide.vue                      # 头部指南按钮组件
├── languages/modules/
│   ├── zh.ts                          # 中文语言包
│   └── en.ts                          # 英文语言包
├── views/
│   ├── project/index.vue              # 项目页面（已集成指南）
│   └── guide-test/index.vue           # 指南测试页面
└── components/D3Topology/components/
    └── FloatingButtons.vue            # 拓扑控制按钮（已添加ID）
```

## 快速开始

1. **启动指南**: 点击头部工具栏的问号图标
2. **测试功能**: 访问 `/guide-test` 页面进行功能测试
3. **自定义指南**: 参考 `useGuide.ts` 中的示例代码

## 最佳实践

- 使用智能指南 (`startAutoGuide`) 获得最佳用户体验
- 为重要功能元素添加稳定的ID或类名
- 保持指南步骤简洁明了
- 定期测试指南在不同页面的表现
